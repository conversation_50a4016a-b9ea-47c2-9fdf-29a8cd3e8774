<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clarion AI</title>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C15N8N9SPE"></script>
    <script src="http://localhost:8097"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-C15N8N9SPE');
    </script>
    <link rel="stylesheet" href="/styles/globals.css">
{{--    <style id="reset-css"> @layer figreset,figoverridable,reset,theme,base,figutils,components,utilities;@layer figreset{:root{--100dvw:100vw;--100dvh:100vh;--banner-height:48px;--full-height-with-banner:calc(100dvh - var(--banner-height));font-synthesis:none;text-align:left}@supports (width:100dvw){:root{--100dvw:100dvw;--100dvh:100dvh}}}.wrapper-with-banner .min-h-screen{min-height:var(--full-height-with-banner)}.wrapper-with-banner .h-screen{height:var(--full-height-with-banner)}</style>--}}
    <style id="ssr-css">#container .css-md8qju {transition-property: transform; transition-duration: 0.2s; transition-timing-function: ease-in-out;}#container .css-r91w0a {transform: translateY(var(--banner-height));}#container .css-vu19p2 {height: var(--full-height-with-banner);}#container .css-7tvcgb {position: fixed;}#container .css-324jje {inset: 0; overflow: auto;}#container .css-vh2lqg {width: var(--viewport-width-scaled); min-height: var(--viewport-height-scaled); height: 100%; top: 0px;}#container .css-tzn6qh {display: block; position: absolute;}#container .css-6gkcj1 {transform-origin: top left; --max-layout-width: 2048px; --min-layout-width: 320px; --max-font-size: 288px; --min-font-size: 6px; --viewport-width-scaled: calc(var(--100dvw) / var(--viewport-scale)); --viewport-height-scaled: calc(var(--100dvh) / var(--viewport-scale)); --content-width-scaled: calc(var(--content-width-unscaled) / var(--viewport-scale)); --content-margin-x-scaled: max(calc((var(--viewport-width) - var(--max-layout-width)) / var(--viewport-scale) / 2), 0px);}#container .css-myl2ny {position: relative; flex-shrink: 0; flex: 1 0 0; display: block;}#container .css-exq74d {min-width: 1px; min-height: 1px; width: 100%; height: 100dvh;}#container .css-gs60ek {overflow: visible;}#container .css-j9f0op {width: 100%; height: 100%;}</style>
    <script>window.__serverRenderedCSSClassNames = {"css-md8qju":"{\"transitionProperty\":\"transform\",\"transitionDuration\":\"0.2s\",\"transitionTimingFunction\":\"ease-in-out\"}","css-r91w0a":"{\"transform\":\"translateY(var(--banner-height))\"}","css-vu19p2":"{\"height\":\"var(--full-height-with-banner)\"}","css-7tvcgb":"{\"position\":\"fixed\"}","css-324jje":"{\"inset\":\"0\",\"overflow\":\"auto\"}","css-vh2lqg":"{\"width\":\"var(--viewport-width-scaled)\",\"minHeight\":\"var(--viewport-height-scaled)\",\"height\":\"100%\",\"top\":\"0px\"}","css-tzn6qh":"{\"display\":\"block\",\"position\":\"absolute\"}","css-6gkcj1":"{\"transformOrigin\":\"top left\",\"--max-layout-width\":\"2048px\",\"--min-layout-width\":\"320px\",\"--max-font-size\":\"288px\",\"--min-font-size\":\"6px\",\"--viewport-width-scaled\":\"calc(var(--100dvw) / var(--viewport-scale))\",\"--viewport-height-scaled\":\"calc(var(--100dvh) / var(--viewport-scale))\",\"--content-width-scaled\":\"calc(var(--content-width-unscaled) / var(--viewport-scale))\",\"--content-margin-x-scaled\":\"max(calc((var(--viewport-width) - var(--max-layout-width)) / var(--viewport-scale) / 2), 0px)\"}","css-myl2ny":"{\"position\":\"relative\",\"flexShrink\":0,\"flex\":\"1 0 0\",\"display\":\"block\"}","css-exq74d":"{\"minWidth\":1,\"minHeight\":1,\"width\":\"100%\",\"height\":\"100dvh\"}","css-gs60ek":"{\"overflow\":\"visible\"}","css-j9f0op":"{\"width\":\"100%\",\"height\":\"100%\"}"}</script>
    @viteReactRefresh
    @vite('public/App.tsx') {{-- For Vite --}}
    {{-- <script src="{{ mix('js/app.js') }}"></script> --}} {{-- For Laravel Mix --}}
</head>
<body>
<div id="container">
    <div class="">
        <div class="">
            <div class="tailwind">
                <div id="app"></div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
