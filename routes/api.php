<?php

use App\Models\contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use Google\Cloud\RecaptchaEnterprise\V1\RecaptchaEnterpriseServiceClient;
use Google\Cloud\RecaptchaEnterprise\V1\Event;
use Google\Cloud\RecaptchaEnterprise\V1\Assessment;
use Google\Cloud\RecaptchaEnterprise\V1\TokenProperties\InvalidReason;
/**
 * Create an assessment to analyze the risk of a UI action.
 * @param string $recaptchaKey The reCAPTCHA key associated with the site/app
 * @param string $token The generated token obtained from the client.
 * @param string $project Your Google Cloud Project ID.
 * @param string $action Action name corresponding to the token.
 */
function create_assessment(
    string $recaptchaKey,
    string $token,
    string $project,
    string $action
): void {
    // Create the reCAPTCHA client.
    // TODO: Cache the client generation code (recommended) or call client.close() before exiting the method.
    $client = new RecaptchaEnterpriseServiceClient();
    $projectName = $client->projectName($project);

    // Set the properties of the event to be tracked.
    $event = (new Event())
        ->setSiteKey($recaptchaKey)
        ->setToken($token);

    // Build the assessment request.
    $assessment = (new Assessment())
        ->setEvent($event);

    try {
        $response = $client->createAssessment(
            $projectName,
            $assessment
        );

        // Check if the token is valid.
        if ($response->getTokenProperties()->getValid() == false) {
            printf('The CreateAssessment() call failed because the token was invalid for the following reason: ');
            printf(InvalidReason::name($response->getTokenProperties()->getInvalidReason()));
            return;
        }

        // Check if the expected action was executed.
        if ($response->getTokenProperties()->getAction() == $action) {
            // Get the risk score and the reason(s).
            // For more information on interpreting the assessment, see:
            // https://cloud.google.com/recaptcha-enterprise/docs/interpret-assessment
            printf('The score for the protection action is:');
            printf($response->getRiskAnalysis()->getScore());
        } else {
            printf('The action attribute in your reCAPTCHA tag does not match the action you are expecting to score');
        }
    } catch (exception $e) {
        printf('CreateAssessment() call failed with the following error: ');
        printf($e);
    }
}


Route::get('/contact/submit', function (Request $request) {
    $client = new GuzzleHttp\Client();
    $key = config('services.recaptcha.secret');
    $sitekey = config('services.recaptcha.sitekey');
//    $payload = ['secret' => $key, 'response' => $request->get('captcha_response')];
//    $res = $client->post('https://www.google.com/recaptcha/api/siteverify', ['form_params'=>$payload]);
//    $resp = json_decode($res->getBody()->getContents());
    $ret = create_assessment(
        $sitekey,
        $request->get('captcha_response'),
        'kodiyt-335922',
        'signup'
    );
    if($ret){
        $contact = new \App\Models\contact();

        $contact->name = $request->get('name');
        $contact->email = $request->get('email');
        $contact->phone = $request->get('phone');
        $contact->reason = $request->get('reason');
        $contact->save();
        return true;
    } else {
        return false;
    }
});

Route::get('/contact/all', function (Request $request) {
    $startDate = $request->get('startDate');
    $endDate = $request->get('endDate');
    $contacts = contact::withTrashed();
    if($startDate){
        $contacts = $contacts->where('created_at', '>=', $startDate);
    }
    if($endDate) {
        $contacts = $contacts->where('created_at', '<=', $endDate);
    }
    $contacts = $contacts->get();
    $headers = Schema::getColumnListing('contacts');
    $csvFileName = 'user.csv';
    $csvFile = fopen($csvFileName, 'w');
    fputcsv($csvFile, $headers);
    foreach ($contacts as $contact) {
        fputcsv($csvFile, $contact->toArray());
    }


    fclose($csvFile);
    // Download the CSV file
    return Response::download(public_path($csvFileName))->deleteFileAfterSend(true);
})->middleware(App\Http\Middleware\Authenticate::class);


Route::get('/contact/new', function (Request $request) {
    $contacts = contact::all();

    $csvFileName = 'user.csv';
    $csvFile = fopen($csvFileName, 'w');
    $headers = Schema::getColumnListing('contacts');
    fputcsv($csvFile, $headers);
    foreach ($contacts as $contact) {
        fputcsv($csvFile, $contact->toArray());
        $contact->delete();
    }

    fclose($csvFile);

// Download the CSV file
    return Response::download(public_path($csvFileName))->deleteFileAfterSend(true);
})->middleware(App\Http\Middleware\Authenticate::class);

