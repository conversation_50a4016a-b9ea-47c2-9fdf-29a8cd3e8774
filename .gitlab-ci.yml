stages:
  - verify
  - build
  - deploy

variables:
  STAGE_PATH: "/home/<USER>/public_html/stage.v2.clarionailaw.com"
  PROD_PATH: "/home/<USER>/public_html/clarionailawwebsitev2"

require-stage-merge:
  stage: verify
  only:
    - main
  script:
    - echo "Checking that stage has been merged into main..."
    - git fetch origin stage
    - if ! git merge-base --is-ancestor origin/stage HEAD; then
      echo "❌ You must merge 'stage' into 'main' before merging to main!";
      exit 1;
      fi

deploy-to-server:
  stage: deploy
  only:
    - main
    - stage
  before_script:
    # Configure SSH
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 -d | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H **************  >> ~/.ssh/known_hosts
  script:
    - |
      if [ "$CI_COMMIT_BRANCH" = "main" ]; then
      DEPLOY_PATH=$PROD_PATH
      else
      DEPLOY_PATH=$STAGE_PATH
      fi
      ssh imtno5tvspx9@************** "
      ls /home
      cd $DEPLOY_PATH &&
      git reset --hard HEAD
      git pull origin $CI_COMMIT_BRANCH &&
      composer update --no-interaction --prefer-dist --optimize-autoloader &&
      npm install &&
      npm run build"
