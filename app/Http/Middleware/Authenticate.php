<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpFoundation\Response;
//IaCQxN4gBH0EcMj37uLz6oL9dsyvq8dX
class Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $accounts =[];
        $accounts['clarionaiadmin'] = '$2y$12$Z9EaNq0QAAOJElX3Z6hY5.jSJaowfx8HAYJK1T.4GadZAzehbw5Ye';
        $hashed = Hash::make($request->getPassword(), [
            'memory' => 1024,
            'time' => 2,
            'threads' => 2,
        ]);
        if (!Hash::check($request->getPassword(), $accounts[$request->getUser()]??'')) {
            $headers = array('WWW-Authenticate' => 'Basic');
            return response('Unauthorized', 401, $headers);
        }
        return $next($request);
    }
}
