{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.1.12", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.8", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite": "^7.1.3"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@vitejs/plugin-react": "^5.0.2", "class-variance-authority": "^0.7.1", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-google-recaptcha-v3": "^1.11.0", "react-router-dom": "^7.8.2", "tailwind-merge": "^3.3.1"}}