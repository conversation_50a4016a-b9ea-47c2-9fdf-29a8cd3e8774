import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Check<PERSON>ircle, Zap, Shield, ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function ServicesUnifiedVariation3() {
  const services = [
    {
      title: "AI Readiness",
      subtitle: "Get AI-ready with confidence",
      description: "Assess your organization's AI readiness, develop governance frameworks, and create implementation roadmaps that ensure responsible AI adoption.",
      href: "/ai-readiness",
      icon: CheckCircle,
      gradient: "from-brand-purple to-brand-purple/80",
      stats: "90% faster compliance setup"
    },
    {
      title: "AI Builds",
      subtitle: "Custom AI solutions for your business", 
      description: "Design, develop, and deploy tailored AI solutions that integrate seamlessly with your existing systems and meet enterprise standards.",
      href: "/ai-builds",
      icon: Zap,
      gradient: "from-brand-orange to-brand-orange/80",
      stats: "50% reduction in development time"
    },
    {
      title: "AI Counsel",
      subtitle: "Expert legal guidance for AI initiatives",
      description: "Navigate complex AI regulations, ensure compliance, and mitigate risks with expert legal counsel from AI law specialists.",
      href: "/ai-counsel",
      icon: Shield,
      gradient: "from-brand-red to-brand-red/80",
      stats: "100% regulatory compliance"
    }
  ];

  return (
    <section id="services" className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">
            Guiding every step of your AI journey
          </h2>
          <p className="text-xl text-brand-teal">
            We empower you to adopt, build, and scale AI responsibly
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div key={index} className="group cursor-pointer">
              <Card className="bg-white border-0 shadow-lg h-full flex flex-col hover:shadow-2xl transition-all duration-500 group-hover:-translate-y-2 relative overflow-hidden">
                {/* Animated gradient background that appears on hover */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
                
                <CardHeader className="text-center pb-0 pt-8 relative z-10">
                  <div className="flex items-center justify-center mb-6">
                    <div className="bg-white rounded-full p-4 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <service.icon className="h-10 w-10 text-brand-navy group-hover:text-brand-red transition-colors duration-300" />
                    </div>
                  </div>
                  
                  {/* Stats badge */}
                  <div className="mb-4">
                    <span className="inline-block bg-brand-light-gray group-hover:bg-white/20 group-hover:backdrop-blur-sm px-3 py-1 rounded-full text-sm font-semibold text-brand-navy group-hover:text-white transition-all duration-300">
                      {service.stats}
                    </span>
                  </div>
                  
                  <CardTitle className="text-3xl font-bold mb-6 text-brand-navy group-hover:text-white transition-colors duration-300">
                    {service.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="flex-1 flex flex-col px-8 relative z-10">
                  <p className="text-lg leading-relaxed text-brand-navy group-hover:text-white/90 mb-4 font-semibold transition-colors duration-300">
                    {service.subtitle}
                  </p>
                  <p className="text-lg leading-relaxed text-brand-teal group-hover:text-white/80 mb-8 flex-1 font-medium transition-colors duration-300">
                    {service.description}
                  </p>
                  
                  <div className="mt-auto">
                    <Link 
                      to={service.href}
                      onClick={() => {
                        setTimeout(() => window.scrollTo(0, 0), 0);
                      }}
                    >
                      <Button 
                        className="w-full bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red group-hover:bg-white group-hover:text-brand-navy group-hover:border-white transition-all duration-300 text-lg py-6 text-white flex items-center justify-center gap-2"
                      >
                        Learn more
                        <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>

                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-white/10 to-transparent group-hover:from-white/20 transition-all duration-300"></div>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}