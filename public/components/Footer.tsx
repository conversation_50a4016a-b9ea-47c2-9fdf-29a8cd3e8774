import { Separator } from "./ui/separator";
import { Linkedin } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import clarionLogoWhite from '/public/asset/7045abf2cbe20b1738b44a8c6fc940f07ea76b50.png';

export function Footer() {
  return (
    <footer className="bg-brand-navy/90 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <img
                src={clarionLogoWhite}
                alt="Clarion AI Partners"
                className="h-10 w-auto"
              />
            </div>
            <p className="text-brand-light-gray opacity-80">
              Expert legal, technical, and strategic support to help you adopt AI you can trust
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Services</h4>
            <ul className="space-y-2 text-brand-light-gray opacity-80">
              <li>
                <Link
                  to="/ai-readiness"
                  className="hover:text-white transition-colors"
                  onClick={() => {
                    setTimeout(() => window.scrollTo(0, 0), 0);
                  }}
                >
                  AI Readiness
                </Link>
              </li>
              <li>
                <Link
                  to="/ai-builds"
                  className="hover:text-white transition-colors"
                  onClick={() => {
                    setTimeout(() => window.scrollTo(0, 0), 0);
                  }}
                >
                  AI Builds
                </Link>
              </li>
              <li>
                <Link
                  to="/ai-counsel"
                  className="hover:text-white transition-colors"
                  onClick={() => {
                    setTimeout(() => window.scrollTo(0, 0), 0);
                  }}
                >
                  AI Counsel
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Company</h4>
            <ul className="space-y-2 text-brand-light-gray opacity-80">
              <li>
                <Link
                  to="/public"
                  className="hover:text-white transition-colors"
                  onClick={() => {
                    setTimeout(() => window.scrollTo(0, 0), 0);
                  }}
                >
                  Home
                </Link>
              </li>
              <li><Link to="/about" className="hover:text-white transition-colors" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>About</Link></li>
              <li><Link to="/lets-talk" className="hover:text-white transition-colors" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>Contact</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Connect</h4>
            <div className="flex space-x-4">
              <a href="https://www.linkedin.com/company/clarion-ai-partners/" target="_blank" rel="noopener noreferrer" className="text-brand-light-gray opacity-80 hover:text-white transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        <Separator className="my-8 bg-brand-teal" />

        <div className="flex flex-col md:flex-row justify-between items-center text-brand-light-gray opacity-80">
          <p>&copy; 2025 Clarion AI Partners. All rights reserved.</p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-white transition-colors">Terms of service</a>
          </div>
        </div>
      </div>
    </footer>
  );
}
