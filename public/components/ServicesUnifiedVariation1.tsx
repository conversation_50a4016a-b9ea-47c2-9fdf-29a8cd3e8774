import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "./ui/card";
import { Badge } from "./ui/badge";
import { CheckCircle, Zap, Shield, Users } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function ServicesUnifiedVariation1() {
  const services = [
    {
      title: "AI Readiness",
      subtitle: "Get AI-ready with confidence",
      description: "Assess your organization's AI readiness, develop governance frameworks, and create implementation roadmaps that ensure responsible AI adoption.",
      href: "/ai-readiness",
      icon: CheckCircle,
      color: "bg-brand-purple",
      borderColor: "border-brand-purple",
      hoverColor: "hover:bg-brand-purple/5",
      badge: null
    },
    {
      title: "AI Builds",
      subtitle: "Custom AI solutions for your business",
      description: "Design, develop, and deploy tailored AI solutions that integrate seamlessly with your existing systems and meet enterprise standards.",
      href: "/ai-builds",
      icon: Zap,
      color: "bg-brand-orange",
      borderColor: "border-brand-orange",
      hoverColor: "hover:bg-brand-orange/5",
      badge: "Most Popular"
    },
    {
      title: "AI Counsel",
      subtitle: "Expert legal guidance for AI initiatives",
      description: "Navigate complex AI regulations, ensure compliance, and mitigate risks with expert legal counsel from AI law specialists.",
      href: "/ai-counsel",
      icon: Shield,
      color: "bg-brand-red",
      borderColor: "border-brand-red",
      hoverColor: "hover:bg-brand-red/5",
      badge: null
    }
  ];

  return (
    <section id="services" className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">
            Guiding every step of your AI journey
          </h2>
          <p className="text-xl text-brand-teal">
            We empower you to adopt, build, and scale AI responsibly
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`bg-white border-2 ${service.borderColor} shadow-lg h-full flex flex-col ${service.hoverColor} hover:shadow-2xl hover:scale-105 transition-all duration-300 group relative overflow-hidden`}>
              {service.badge && (
                <div className="absolute top-4 right-4 z-10">
                  <Badge className="bg-brand-navy text-white">
                    {service.badge}
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-0 pt-8 relative">
                <div className="flex items-center justify-center mb-8">
                  <div className={`${service.color} rounded-full p-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <service.icon className="h-10 w-10 text-white" />
                  </div>
                </div>
                <CardTitle className="text-3xl font-bold mb-8 text-brand-navy group-hover:text-brand-navy transition-colors duration-300">
                  {service.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col px-8">
                <p className="text-lg leading-relaxed text-brand-navy mb-4 font-semibold">
                  {service.subtitle}
                </p>
                <p className="text-lg leading-relaxed text-brand-teal mb-8 flex-1 font-medium">
                  {service.description}
                </p>
                
                <div className="mt-auto">
                  <Link 
                    to={service.href}
                    onClick={() => {
                      setTimeout(() => window.scrollTo(0, 0), 0);
                    }}
                  >
                    <Button 
                      className={`w-full ${service.color} border-2 ${service.borderColor} hover:bg-white hover:text-brand-navy hover:${service.borderColor} transition-all duration-300 text-lg py-6 text-white`}
                    >
                      Learn more
                    </Button>
                  </Link>
                </div>
              </CardContent>
              
              {/* Decorative element */}
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${service.color} opacity-60`}></div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}