
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title} from "./ui/card";

export function ValueStatements() {
    const statements = [
        "We delivered an AI roadmap that a $12B industrial firm's board unanimously approved for enterprise rollout.",
        "Our structured approach transformed AI from an IT experiment into a board-endorsed growth strategy.",
        "We turned AI skepticism into support by showing early ROI, paving the way for enterprise-wide adoption at a multinational manufacturer.",
        "Our AI governance framework gave a $500B+ retailer board-ready confidence in deploying customer-facing AI tools.",
        // "We helped a global logistics company redesign its AI pilot strategy, reducing redundant spend and saving $18M annually."
    ];

    return (

        <section className="py-16 bg-brand-light-gray overflow-hidden">
            <div className="max-w-6xl mx-auto px-4">
                <p className="text-center text-brand-teal text-lg font-medium mb-8">
                    Proven results
                </p>

                {/* Scrolling container */}
                <div className="relative">
                    <div className="flex center">

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {statements.map((statement, index) => (

                                <Card key={index}
                                      className="bg-white border border-brand-light-gray hover:shadow-lg transition-shadow duration-300">
                                    <CardHeader className="text-center">
                                        <CardTitle></CardTitle>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-brand-teal mb-4 text-base">
                                            {statement}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))
                            };
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
