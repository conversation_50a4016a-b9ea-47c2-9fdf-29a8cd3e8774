import { useState } from "react";
import { Button } from "./ui/button";
import { Link, useLocation } from "react-router-dom";
import { Menu, X } from "lucide-react";
import clarionLogo from '/public/asset/f7cc63a9405bf75a0a5c7601f7fd47012be773eb.png';

export function Header() {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const getLinkClassName = (path: string, isMobile = false) => {
    const isActive = location.pathname === path;
    const baseClasses = "transition-all duration-300";
    const desktopClasses = "relative hover:text-brand-red";
    const mobileClasses = "block px-4 py-2 hover:bg-brand-light-gray rounded-lg";

    if (isActive) {
      return `${baseClasses} ${isMobile ? mobileClasses : desktopClasses} font-semibold text-brand-red ${!isMobile ? 'after:content-[""] after:absolute after:bottom-[-8px] after:left-0 after:right-0 after:h-0.5 after:bg-brand-red after:rounded-full' : 'bg-brand-light-gray'}`;
    }

    return `${baseClasses} ${isMobile ? mobileClasses : desktopClasses} text-brand-navy hover:text-brand-red`;
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <header className="w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/public">
            <img
              src={clarionLogo}
              alt="Clarion AI Partners"
              className="h-12 w-auto"
            />
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center">
          {/* Service offerings group */}
          <div className="flex items-center space-x-8">
            <Link to="/ai-readiness" className={getLinkClassName('/ai-readiness')}>AI Readiness</Link>
            <Link to="/ai-builds" className={getLinkClassName('/ai-builds')}>AI Builds</Link>
            <Link to="/ai-counsel" className={getLinkClassName('/ai-counsel')}>AI Counsel</Link>
          </div>

          {/* About link with more spacing */}
          <div className="flex items-center space-x-8 ml-16">
            <Link to="/about" className={getLinkClassName('/about')}>About</Link>
          </div>
        </nav>

        {/* Right side - CTA Button and Mobile Menu */}
        <div className="flex items-center gap-4">
          {/* CTA Button - Always visible */}
          <Link to="/lets-talk">
            <Button className="bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300 text-sm px-4 py-2">
              Let's talk
            </Button>
          </Link>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 text-brand-navy hover:text-brand-red transition-colors"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t bg-white/95 backdrop-blur">
          <nav className="container mx-auto px-4 py-4 space-y-2">
            <Link
              to="/ai-readiness"
              className={getLinkClassName('/ai-readiness', true)}
              onClick={closeMobileMenu}
            >
              AI Readiness
            </Link>
            <Link
              to="/ai-builds"
              className={getLinkClassName('/ai-builds', true)}
              onClick={closeMobileMenu}
            >
              AI Builds
            </Link>
            <Link
              to="/ai-counsel"
              className={getLinkClassName('/ai-counsel', true)}
              onClick={closeMobileMenu}
            >
              AI Counsel
            </Link>
            <Link
              to="/about"
              className={getLinkClassName('/about', true)}
              onClick={closeMobileMenu}
            >
              About
            </Link>
            {/* Temporary Demo Links for Mobile */}
            <Link
              to="/services-variations"
              className="block px-4 py-2 bg-brand-orange text-white rounded-lg hover:bg-brand-orange/80 transition-colors duration-300 font-medium text-center"
              onClick={closeMobileMenu}
            >
              Services Demo
            </Link>

          </nav>
        </div>
      )}
    </header>
  );
}
