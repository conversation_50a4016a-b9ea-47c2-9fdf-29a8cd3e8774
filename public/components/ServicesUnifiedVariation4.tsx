import { But<PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "./ui/card";
import { CheckCircle, Zap, Shield, Star, TrendingUp, Award } from "lucide-react";
import { Link } from "react-router-dom";

export function ServicesUnifiedVariation4() {
  const services = [
    {
      title: "AI Readiness",
      subtitle: "Get AI-ready with confidence",
      description: "Assess your organization's AI readiness, develop governance frameworks, and create implementation roadmaps that ensure responsible AI adoption.",
      href: "/ai-readiness",
      icon: CheckCircle,
      bgColor: "bg-gradient-to-br from-brand-purple/10 to-brand-purple/5",
      iconBg: "bg-brand-purple",
      borderColor: "border-brand-purple/20",
      features: ["Risk Assessment", "Governance Framework", "Implementation Roadmap"],
      primaryColor: "brand-purple"
    },
    {
      title: "AI Builds",
      subtitle: "Custom AI solutions for your business",
      description: "Design, develop, and deploy tailored AI solutions that integrate seamlessly with your existing systems and meet enterprise standards.",
      href: "/ai-builds",
      icon: Zap,
      bgColor: "bg-gradient-to-br from-brand-orange/10 to-brand-orange/5",
      iconBg: "bg-brand-orange",
      borderColor: "border-brand-orange/20",
      features: ["Custom Development", "System Integration", "Enterprise Standards"],
      primaryColor: "brand-orange",
      badge: "Most Popular"
    },
    {
      title: "AI Counsel",
      subtitle: "Expert legal guidance for AI initiatives",
      description: "Navigate complex AI regulations, ensure compliance, and mitigate risks with expert legal counsel from AI law specialists.",
      href: "/ai-counsel",
      icon: Shield,
      bgColor: "bg-gradient-to-br from-brand-red/10 to-brand-red/5",
      iconBg: "bg-brand-red",
      borderColor: "border-brand-red/20",
      features: ["Regulatory Compliance", "Risk Mitigation", "Legal Frameworks"],
      primaryColor: "brand-red"
    }
  ];

  return (
    <section id="services" className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">
            Guiding every step of your AI journey
          </h2>
          <p className="text-xl text-brand-teal">
            We empower you to adopt, build, and scale AI responsibly
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`${service.bgColor} border-2 ${service.borderColor} h-full flex flex-col hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 group relative overflow-hidden`}>
              {service.badge && (
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-brand-navy text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    {service.badge}
                  </div>
                </div>
              )}
              
              <CardHeader className="text-center pb-0 pt-8">
                <div className="flex items-center justify-center mb-6">
                  <div className={`${service.iconBg} rounded-2xl p-4 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300`}>
                    <service.icon className="h-10 w-10 text-white" />
                  </div>
                </div>
                <CardTitle className="text-3xl font-bold mb-4 text-brand-navy">
                  {service.title}
                </CardTitle>
                <p className="text-lg leading-relaxed text-brand-navy mb-6 font-semibold">
                  {service.subtitle}
                </p>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col px-8">
                <p className="text-brand-teal mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                {/* Feature List */}
                <div className="mb-8">
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className={`w-2 h-2 ${service.iconBg} rounded-full`}></div>
                        <span className="text-brand-teal font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="mt-auto">
                  <Link 
                    to={service.href}
                    onClick={() => {
                      setTimeout(() => window.scrollTo(0, 0), 0);
                    }}
                  >
                    <Button 
                      className={`w-full ${service.iconBg} border-2 border-transparent hover:bg-white hover:text-brand-navy hover:border-current transition-all duration-300 text-lg py-6 text-white group-hover:shadow-lg`}
                    >
                      Get started
                    </Button>
                  </Link>
                </div>
              </CardContent>

              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-current to-transparent opacity-20"></div>
              <div className="absolute bottom-0 right-0 w-20 h-20 bg-gradient-to-tl from-white/10 to-transparent rounded-tl-full"></div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}