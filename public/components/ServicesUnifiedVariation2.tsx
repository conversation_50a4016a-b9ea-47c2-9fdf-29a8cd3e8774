import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { <PERSON><PERSON>ircle, Zap, Shield } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function ServicesUnifiedVariation2() {
  const services = [
    {
      title: "AI Readiness",
      subtitle: "Get AI-ready with confidence",
      description: "Assess your organization's AI readiness, develop governance frameworks, and create implementation roadmaps that ensure responsible AI adoption.",
      href: "/ai-readiness",
      icon: CheckCircle,
      featured: false
    },
    {
      title: "AI Builds",
      subtitle: "Custom AI solutions for your business",
      description: "Design, develop, and deploy tailored AI solutions that integrate seamlessly with your existing systems and meet enterprise standards.",
      href: "/ai-builds",
      icon: Zap,
      featured: true // This will be the featured/larger card
    },
    {
      title: "AI Counsel",
      subtitle: "Expert legal guidance for AI initiatives",
      description: "Navigate complex AI regulations, ensure compliance, and mitigate risks with expert legal counsel from AI law specialists.",
      href: "/ai-counsel",
      icon: Shield,
      featured: false
    }
  ];

  return (
    <section id="services" className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">
            Guiding every step of your AI journey
          </h2>
          <p className="text-xl text-brand-teal">
            We empower you to adopt, build, and scale AI responsibly
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        {/* Featured Layout: 2x2 grid where middle card spans 2 rows */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 md:grid-rows-2">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className={`bg-white border-0 shadow-lg flex flex-col hover:shadow-2xl transition-all duration-300 group relative overflow-hidden
                ${service.featured 
                  ? 'lg:col-span-2 lg:row-span-2 md:col-span-2 bg-gradient-to-br from-brand-red via-brand-red to-brand-red/90 text-white scale-105' 
                  : 'hover:scale-105'
                }`}
            >
              {service.featured && (
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                    <span className="text-white font-semibold text-sm">Featured</span>
                  </div>
                </div>
              )}
              
              <CardHeader className="text-center pb-0 pt-8">
                <div className="flex items-center justify-center mb-8">
                  <div className={`rounded-full p-4 shadow-lg group-hover:scale-110 transition-transform duration-300
                    ${service.featured 
                      ? 'bg-white/20 backdrop-blur-sm' 
                      : 'bg-brand-red/10 group-hover:bg-brand-red/20'
                    }`}>
                    <service.icon className={`h-10 w-10 ${service.featured ? 'text-white' : 'text-brand-red'}`} />
                  </div>
                </div>
                <CardTitle className={`text-3xl font-bold mb-8 transition-colors duration-300
                  ${service.featured ? 'text-white' : 'text-brand-navy'}
                `}>
                  {service.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col px-8">
                <p className={`text-lg leading-relaxed mb-4 font-semibold
                  ${service.featured ? 'text-white/90' : 'text-brand-navy'}
                `}>
                  {service.subtitle}
                </p>
                <p className={`text-lg leading-relaxed mb-8 flex-1 font-medium
                  ${service.featured ? 'text-white/80' : 'text-brand-teal'}
                `}>
                  {service.description}
                </p>
                
                <div className="mt-auto">
                  <Link 
                    to={service.href}
                    onClick={() => {
                      setTimeout(() => window.scrollTo(0, 0), 0);
                    }}
                  >
                    <Button 
                      className={`w-full transition-all duration-300 text-lg py-6
                        ${service.featured 
                          ? 'bg-white text-brand-red border-2 border-white hover:bg-white/90 hover:scale-105' 
                          : 'bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red text-white hover:scale-105'
                        }`}
                    >
                      Learn more
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}