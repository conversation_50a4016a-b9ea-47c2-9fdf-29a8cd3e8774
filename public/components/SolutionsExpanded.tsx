import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Lightbulb, Hammer, Scale, CheckCircle, ArrowRight } from "lucide-react";

const services = [
  {
    icon: Lightbulb,
    color: "bg-brand-orange",
    title: "AI Readiness",
    subtitle: "For organizations preparing to adopt AI for the first time or looking to take a more strategic, scalable approach.",
    description: "AI is powerful, but it's not plug-and-play. Off-the-shelf tools often overpromise and underdeliver. Real results require thoughtful alignment across infrastructure, use cases, people, and safeguards.",
    value: "Clarion's AI Readiness program equips your team to adopt AI confidently and responsibly. Whether you're just exploring or already experimenting, we meet you where you are and deliver clear, strategic guidance you can act on.",
    questions: [
      "Is your technical infrastructure prepared for AI workloads?",
      "Are your use cases realistic and supported by quality data?",
      "Do you have the right people, policies, and guardrails in place?",
      "Are your teams trained to adopt AI successfully and responsibly?"
    ],
    process: [
      {
        title: "Discovery and Diagnostics",
        description: "Evaluate your systems, teams, and current AI posture"
      },
      {
        title: "Strategy Development", 
        description: "Prioritize use cases, select models, and align stakeholders"
      },
      {
        title: "Action Plan and Roadmap",
        description: "Deliver clear next steps, governance guidance, and decision-ready recommendations"
      }
    ]
  },
  {
    icon: Hammer,
    color: "bg-brand-purple",
    title: "AI Implementation",
    subtitle: "For organizations with scoped use cases and data readiness, ready to design and deploy AI systems that work and hold up to scrutiny.",
    description: "If you've already identified a valuable use case, Clarion can help you bring it to life with confidence and control.",
    value: "Our team designs and builds custom AI systems with compliance and governance integrated from the start. We specialize in advanced architectures like Retrieval-Augmented Generation (RAG), agentic AI, and constitutional frameworks. Our systems are not only powerful but provably responsible.",
    questions: [
      "Designing AI systems that meet performance, compliance, and auditability standards",
      "Building with modularity and traceability to support long-term scale",
      "Embedding governance and monitoring to keep systems aligned with your values"
    ],
    process: [
      {
        title: "Design and Architecture",
        description: "Align business needs with technical and regulatory requirements"
      },
      {
        title: "Development and Deployment",
        description: "Build, test, and launch custom AI systems"
      },
      {
        title: "Oversight and Evaluation",
        description: "Implement monitoring, feedback loops, and compliance documentation"
      }
    ]
  },
  {
    icon: Scale,
    color: "bg-brand-red",
    title: "AI Counsel",
    subtitle: "For organizations seeking legal, ethical, and strategic guidance to govern and adopt AI with confidence.",
    description: "AI isn't just a technical challenge. It carries significant legal and reputational considerations. Clarion's AI Counsel services help organizations navigate the regulatory, ethical, and compliance landscape with clarity and confidence.",
    value: "Whether you need guidance on bias testing, governance frameworks, or regulatory strategy, our team brings unmatched expertise in AI law, privacy, constitutional frameworks, and policymaking.",
    questions: [
      "Legal and regulatory implications of AI use",
      "Bias testing, hallucination risk analysis, and safety audits", 
      "AI governance programs, internal policies, and steering committees",
      "Legislative engagement and public policy strategy"
    ],
    process: [
      {
        title: "Assessment and Risk Mapping",
        description: "Review your AI initiatives and identify legal and ethical risks"
      },
      {
        title: "Governance and Policy Development",
        description: "Create guardrails to support innovation and compliance"
      },
      {
        title: "Strategic Advisory",
        description: "Provide ongoing guidance as regulations, markets, and technologies evolve"
      }
    ]
  }
];

export function SolutionsExpanded() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">Services (Expanded View)</h2>
          <p className="text-lg text-brand-teal max-w-3xl mx-auto">
            Comprehensive AI solutions tailored to your organization's readiness and goals
          </p>
        </div>
        
        <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => (
            <Card key={index} className="flex flex-col h-full border-2 border-gray-100 hover:border-brand-purple transition-all duration-300 hover:shadow-xl">
              <CardHeader className="text-center pb-6">
                <div className={`w-20 h-20 ${service.color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                  <service.icon className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl mb-3 text-brand-navy">{service.title}</CardTitle>
                <p className="text-brand-teal leading-relaxed">{service.subtitle}</p>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col">
                <div className="flex-1 space-y-6">
                  {/* Description */}
                  <div>
                    <p className="text-brand-teal leading-relaxed mb-4">{service.description}</p>
                    <p className="text-brand-navy leading-relaxed">{service.value}</p>
                  </div>
                  
                  {/* Focus Areas */}
                  <div>
                    <h4 className="text-brand-navy font-semibold mb-3">
                      {service.title === "AI Implementation" ? "We focus on:" : 
                       service.title === "AI Counsel" ? "We advise on:" : "We help you answer:"}
                    </h4>
                    <ul className="space-y-2">
                      {service.questions.map((question, qIndex) => (
                        <li key={qIndex} className="flex items-start gap-2 text-sm text-brand-teal">
                          <CheckCircle className="w-4 h-4 text-brand-purple mt-0.5 flex-shrink-0" />
                          <span>{question}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Process */}
                  <div>
                    <h4 className="text-brand-navy font-semibold mb-3">Our Process:</h4>
                    <div className="space-y-3">
                      {service.process.map((step, stepIndex) => (
                        <div key={stepIndex} className="flex items-start gap-3">
                          <div className="w-6 h-6 bg-brand-light-gray rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <span className="text-xs text-brand-purple font-medium">{stepIndex + 1}</span>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-brand-navy">{step.title}</h5>
                            <p className="text-xs text-brand-teal mt-1">{step.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* CTA Button */}
                <div className="pt-6">
                  <Button 
                    className="w-full bg-brand-purple hover:bg-brand-red text-white transition-colors duration-300"
                    size="lg"
                  >
                    Let's talk
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}