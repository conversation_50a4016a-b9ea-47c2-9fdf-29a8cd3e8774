import { useState } from 'react';
import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { CheckCircle, ArrowRight, Clock, Target, Users, FileText } from "lucide-react";

const deliverables = [
  {
    title: "Current state analysis",
    description: "We assess your organization's current AI capabilities, governance structures, and readiness gaps to establish a baseline for improvement."
  },
  {
    title: "Risk assessment and mitigation strategies",
    description: "Our team identifies potential AI-related risks specific to your industry and organization, developing targeted mitigation approaches."
  },
  {
    title: "Governance framework design",
    description: "We create comprehensive AI governance policies, procedures, and oversight mechanisms tailored to your organizational structure."
  },
  {
    title: "Implementation roadmap",
    description: "You receive a detailed, phased plan for implementing AI governance with clear timelines, milestones, and success metrics."
  }
];

const icons = [CheckCircle, ArrowRight, Target, FileText];

export function ProcessVariations() {
  const [currentVariation, setCurrentVariation] = useState(0);

  const nextVariation = () => {
    setCurrentVariation((prev) => (prev + 1) % 5);
  };

  const renderVariation = () => {
    switch (currentVariation) {
      case 0:
        // Original Timeline Layout
        return (
          <div className="max-w-4xl mx-auto px-4">
            <h3 className="text-lg mb-8 text-center text-brand-teal">Variation 1: Timeline Layout (Original)</h3>
            {deliverables.map((deliverable, index) => (
              <div key={index} className="relative flex items-start mb-12 last:mb-0">
                {index < deliverables.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-20 bg-slate-200"></div>
                )}
                <div className="flex-shrink-0 w-12 h-12 bg-brand-red text-white rounded-full flex items-center justify-center text-sm font-medium mr-6">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <h4 className="text-xl text-brand-navy mb-2">
                    {deliverable.title}
                  </h4>
                  <p className="text-base text-brand-teal leading-relaxed">
                    {deliverable.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        );

      case 1:
        // Card Grid Layout
        return (
          <div className="max-w-6xl mx-auto px-4">
            <h3 className="text-lg mb-8 text-center text-brand-teal">Variation 2: Card Grid Layout</h3>
            <div className="grid md:grid-cols-2 gap-8">
              {deliverables.map((deliverable, index) => {
                const Icon = icons[index];
                return (
                  <Card key={index} className="border-2 border-brand-light-gray hover:border-brand-red transition-colors duration-300">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-brand-red rounded-lg flex items-center justify-center">
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <CardTitle className="text-xl text-brand-navy">{deliverable.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-brand-teal leading-relaxed">{deliverable.description}</p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        );

      case 2:
        // Horizontal Process Flow
        return (
          <div className="max-w-6xl mx-auto px-4">
            <h3 className="text-lg mb-8 text-center text-brand-teal">Variation 3: Horizontal Process Flow</h3>
            <div className="flex flex-col lg:flex-row gap-4 lg:gap-2">
              {deliverables.map((deliverable, index) => (
                <div key={index} className="flex-1 relative">
                  <div className="bg-white border-2 border-brand-light-gray rounded-lg p-6 h-full">
                    <div className="text-center mb-4">
                      <div className="w-16 h-16 bg-brand-navy text-white rounded-full flex items-center justify-center text-xl font-medium mx-auto mb-3">
                        {index + 1}
                      </div>
                      <h4 className="text-lg text-brand-navy mb-3">{deliverable.title}</h4>
                    </div>
                    <p className="text-brand-teal text-sm leading-relaxed">{deliverable.description}</p>
                  </div>
                  {index < deliverables.length - 1 && (
                    <div className="hidden lg:block absolute -right-1 top-1/2 transform -translate-y-1/2 z-10">
                      <ArrowRight className="w-6 h-6 text-brand-red" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 3:
        // Accordion Style with Numbers
        return (
          <div className="max-w-4xl mx-auto px-4">
            <h3 className="text-lg mb-8 text-center text-brand-teal">Variation 4: Expanded Card Layout</h3>
            <div className="space-y-6">
              {deliverables.map((deliverable, index) => (
                <div key={index} className="bg-brand-light-gray rounded-lg p-8 relative overflow-hidden">
                  <div className="absolute top-4 right-4 text-6xl font-bold text-brand-red opacity-20">
                    {String(index + 1).padStart(2, '0')}
                  </div>
                  <div className="relative z-10">
                    <h4 className="text-2xl text-brand-navy mb-4">{deliverable.title}</h4>
                    <p className="text-brand-teal leading-relaxed text-lg">{deliverable.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 4:
        // Minimalist List with Emphasis
        return (
          <div className="max-w-4xl mx-auto px-4">
            <h3 className="text-lg mb-8 text-center text-brand-teal">Variation 5: Minimalist List Layout</h3>
            <div className="space-y-12">
              {deliverables.map((deliverable, index) => (
                <div key={index} className="border-l-4 border-brand-red pl-8 py-4">
                  <div className="flex items-start gap-4">
                    <span className="text-3xl font-bold text-brand-red mt-1">
                      {String(index + 1).padStart(2, '0')}
                    </span>
                    <div className="flex-1">
                      <h4 className="text-2xl text-brand-navy mb-3">{deliverable.title}</h4>
                      <p className="text-brand-teal leading-relaxed text-lg">{deliverable.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl mb-6 text-brand-navy">
            Our process
          </h2>
          <Button 
            onClick={nextVariation}
            className="mb-8 bg-brand-orange hover:bg-white hover:text-brand-orange border-2 border-brand-orange"
          >
            Next Variation ({currentVariation + 1}/5)
          </Button>
        </div>
        
        {renderVariation()}
      </div>
    </section>
  );
}