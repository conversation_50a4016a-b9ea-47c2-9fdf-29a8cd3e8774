import { Card, CardContent } from "./ui/card";
import { Quote } from "lucide-react";

export function Testimonials() {
  const testimonials = [
    {
      quote: "What sets <PERSON><PERSON><PERSON> apart is that they actually speak our technical language — and still bring the legal expertise we need to apply AI responsibly. They bridge both worlds in a way no one else does.",
      author: "CISO at F500 financial Services Firm"
    },
    {
      quote: "Clarion is invaluable to us. Their AI legal counsel gives me confidence we're protected, and their deep technical know-how ensures the solutions actually work. That combination makes them a trusted partner.",
      author: "General Counsel @ Fortune 100 Retailer"
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg">
              <CardContent className="p-8">
                <Quote className="w-8 h-8 text-primary mb-4" />
                <p className="text-lg mb-6 italic text-brand-teal">"{testimonial.quote}"</p>
                <p className="text-brand-navy">— {testimonial.author}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}