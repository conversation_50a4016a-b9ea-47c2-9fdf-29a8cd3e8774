import { Scale, Trophy, ShieldCheck } from "lucide-react";

export function WhyClarion() {
  const features = [
    {
      icon: Scale,
      title: "Legal expertise at the core",
      description: "Operate with confidence. Our team combines AI law, privacy compliance, and constitutional frameworks to protect your business.",
      color: "bg-brand-red",
      accent: "border-brand-red"
    },
    {
      icon: Trophy,
      title: "Enterprise-grade leadership",
      description: "Clarion's experts have advised Fortune 100 CEOs, U.S. and foreign policymakers, and global boards through AI transformation.",
      color: "bg-brand-red",
      accent: "border-brand-red"
    },
    {
      icon: ShieldCheck,
      title: "AI solutions, strategically designed and built",
      description: "We design and deploy AI systems that are compliant, auditable, and built to meet enterprise standards.",
      color: "bg-brand-red",
      accent: "border-brand-red"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">What makes <PERSON><PERSON><PERSON> different</h2>
          <p className="text-xl text-brand-teal">
            Trusted by enterprise leaders to navigate AI transformation with confidence and compliance.
          </p>
        </div>
      </div>
        
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className={`relative bg-white rounded-xl shadow-lg border-2 ${feature.accent} p-8 hover:shadow-xl transition-shadow duration-300`}>
              <div className="absolute -top-6 left-8">
                <div className={`w-12 h-12 ${feature.color} rounded-full flex items-center justify-center shadow-lg`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="pt-8">
                <h3 className="text-xl font-semibold mb-4 text-brand-navy">{feature.title}</h3>
                <p className="text-brand-teal leading-relaxed">{feature.description}</p>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-brand-light-gray to-transparent"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}