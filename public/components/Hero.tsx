import { But<PERSON> } from "./ui/button";
import { <PERSON> } from "react-router-dom";
import heroImage from '/public/asset/d32b48c41cd8315a104d42bd23e173157c7b6c58.png';

export function Hero() {
  return (
    <section className="bg-white py-20 md:py-32 relative overflow-hidden">

      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-no-repeat opacity-15"
        style={{
          backgroundImage: `url(${heroImage})`,
          backgroundPosition: 'center 25%'
        }}
      />

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 text-center relative z-10">
        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-brand-navy leading-tight pb-2">
          AI, done <span className="italic">right</span>.
        </h1>
        <p className="text-xl md:text-2xl text-brand-teal mb-8">
          Expert legal, technical, and strategic support to help you adopt AI you can trust
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link to="/lets-talk">
            <Button size="lg" className="text-lg px-8 py-4 w-48 bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300">
              Let's talk
            </Button>
          </Link>
          <Button
            size="lg"
            variant="outline"
            className="text-lg px-8 py-4 w-48 bg-white border-brand-red text-brand-red hover:bg-brand-red hover:text-white transition-colors duration-300"
            onClick={() => {
              const servicesSection = document.getElementById('services');
              if (servicesSection) {
                const yOffset = -20; // Small offset for better positioning
                const y = servicesSection.getBoundingClientRect().top + window.pageYOffset + yOffset;
                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }
            }}
          >
            Explore services
          </Button>
        </div>
      </div>
    </section>
  );
}
