import { <PERSON><PERSON> } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { MapPin, Building2 } from "lucide-react";
import { Link } from "react-router-dom";
import bennettImage from '/public/asset/a6b84e1efdfb8b80ddbe9cb686df6d67bad7bb58.png';
import aaronImage from '/public/asset/e0be01c505893f57e2f49439053976f795f73b96.png';

export function About() {
  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-4xl mx-auto px-4">
        {/* Row 1 - Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-brand-navy">Enterprise AI, guided by experts</h2>
          <p className="text-xl text-brand-teal leading-relaxed">
            C<PERSON><PERSON> combines deep legal experience, cutting-edge technical expertise, and a track record of public policy leadership to help organizations deploy AI with confidence.
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        {/* Row 2 - Principals */}
        <div className="mb-16">
          {/* Our Principals Label */}
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-brand-navy">Our Principals</h3>
          </div>

          {/* Principals Grid */}
          <div className="grid lg:grid-cols-2 gap-12 mb-12">
            {/* Bennett Borden */}
            <div className="bg-slate-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
              <div className="flex flex-col items-center text-center">
                <div className="w-32 h-32 mb-6 rounded-full overflow-hidden shadow-lg">
                  <ImageWithFallback
                    src={bennettImage}
                    alt="Bennett B. Borden, CEO"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h4 className="text-2xl font-bold text-brand-navy mb-2">Bennett B. Borden</h4>
                <div className="flex items-center justify-center mb-4">
                  <Building2 className="w-4 h-4 text-brand-purple mr-2" />
                  <span className="text-brand-purple font-medium">CEO</span>
                </div>
                <p className="text-brand-teal leading-relaxed">
                  Bennett is a global authority on the legal, technological, and policy implications of artificial intelligence.
                  He advised Fortune 500 clients as Chief Data Scientist and Partner at the AmLaw100 firms of DLA Piper and Faegre Drinker.
                </p>
              </div>
            </div>

            {/* Aaron Rieke */}
            <div className="bg-slate-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
              <div className="flex flex-col items-center text-center">
                <div className="w-32 h-32 mb-6 rounded-full overflow-hidden shadow-lg">
                  <ImageWithFallback
                    src={aaronImage}
                    alt="Aaron Rieke, Senior Partner"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h4 className="text-2xl font-bold text-brand-navy mb-2">Aaron Rieke</h4>
                <div className="flex items-center justify-center mb-4">
                  <MapPin className="w-4 h-4 text-brand-purple mr-2" />
                  <span className="text-brand-purple font-medium">Senior Partner, Washington DC</span>
                </div>
                <p className="text-brand-teal leading-relaxed">
                  Aaron is a veteran lawyer-technologist based in Washington, DC. He has served at the Federal Trade Commission,
                  advised the White House and Congress on AI policy, and published high-impact computer science research.
                </p>
              </div>
            </div>
          </div>

          {/* Team Note */}
          <div className="text-center">
            <p className="text-lg text-brand-teal leading-relaxed">
              Backed by a cross-functional team of engineers, strategists, lawyers, and advisors who have helped launch and govern real-world AI systems in enterprise and government.
            </p>
          </div>
        </div>

        {/* Row 3 - Call to Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link to="/about" onClick={() => window.scrollTo(0, 0)}>
            <Button
              size="lg"
              className="bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red text-white px-8 py-4 text-lg transition-colors duration-300"
            >
              Our Story
            </Button>
          </Link>
          <Link to="/about#team">
            <Button
              size="lg"
              variant="outline"
              className="border-brand-red text-brand-red hover:bg-brand-red hover:text-white hover:border-brand-red px-8 py-4 text-lg transition-colors duration-300"
            >
              Meet the team
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
