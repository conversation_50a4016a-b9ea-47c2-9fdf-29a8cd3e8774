import { Shield, Target, CheckSquare, FileText, Scale, BookOpen, Building, Heart, DollarSign } from "lucide-react";
import walmartLogo from '/public/asset/d57aebf7642b7163a1636cbed0954e70c53b81ae.png';
import larryMiller<PERSON>ogo from '/public/asset/ccf95b5a7535717f360d815736e379f55de0755c.png';
import kpmgLogo from '/public/asset/9732f0fbf666ca9738296f3bdd52dfdfb8a72580.png';
import forbesLogo from '/public/asset/5e8c8688032572cc12421fdb9e29d2a6b77d192b.png';
import nytLogo from '/public/asset/3f343ffc604a6c839e31d3f052b78abfa34ab588.png';
import fortuneLogo from '/public/asset/aff38973f76fa2a30f1e682d6ce70c05d235afcf.png';
import lawComLogo from '/public/asset/f898cbfa668171b0ab6c920c1523277ece33b354.png';
import bloombergLawLogo from '/public/asset/3dfd9f1f15bd0d0f094c59ef1ae78374e55a5ee9.png';
import grantThorntonLogo from '/public/asset/c132f2f79255402fd8afa5b8aaeb7141b71e2c66.png';
import trianglePestControlLogo from '/public/asset/aed25dea4b5b75977dc23bb102b7640e112f05ed.png';
import methodLogo from '/public/asset/9590f62d2fd19db1d3cd7dc7304067b7c0a13ac7.png';
import abaJournalLogo from '/public/asset/4243f8f3920efda829d7cbb8bd4317648c612c70.png';

export const services = [
  {
    title: ("Establish company-wide AI oversight policies to keep your business on track and ahead of the curve"),
    category: "governance" as const,
    icon: Shield
  },
  {
    title: ("Conduct audits, testing, and other technical assessments of AI systems"),
    category: "assessment" as const,
    icon: Target
  },
  {
    title: ("Create procedures for identifying and approving AI projects within your organizations"),
    category: "governance" as const,
    icon: CheckSquare
  },
  {
    title: ("Vet potential developers, and draft appropriate vendor agreements"),
    category: "contracts" as const,
    icon: FileText
  },
  {
    title: ("Ensure compliant AI deployments, incorporating expertise on a full range of laws and regulations"),
    category: "compliance" as const,
    icon: Scale
  },
  {
    title: ("Stay informed about new developments in AI law and regulation"),
    category: "advisory" as const,
    icon: BookOpen
  }
];

export const categoryColors = {
  governance: "bg-brand-purple text-white",
  assessment: "bg-brand-orange text-white",
  contracts: "bg-brand-teal text-white",
  compliance: "bg-brand-red text-white",
  advisory: "bg-brand-navy text-white"
} as const;

export const trustLogos = [
  { src: walmartLogo, alt: "Walmart", name: "walmart" },
  { src: larryMillerLogo, alt: "Larry H. Miller Company", name: "larry-miller" },
  { src: kpmgLogo, alt: "KPMG", name: "kpmg" },
  { src: forbesLogo, alt: "Forbes", name: "forbes" },
  { src: nytLogo, alt: "The New York Times", name: "nyt" },
  { src: fortuneLogo, alt: "Fortune", name: "fortune" },
  { src: lawComLogo, alt: "Law.com", name: "law-com" },
  { src: bloombergLawLogo, alt: "Bloomberg Law", name: "bloomberg-law" },
  { src: grantThorntonLogo, alt: "Grant Thornton", name: "grant-thornton" },
  { src: trianglePestControlLogo, alt: "Triangle Pest Control", name: "triangle-pest-control" },
  { src: methodLogo, alt: "Method", name: "method" },
  { src: abaJournalLogo, alt: "ABA Journal", name: "aba-journal" }
];

export const caseExamples = [
  {
    industry: "Retail",
    icon: Building,
    description: "Ensuring AI-powered supply chain tools meet emerging regulatory requirements.",
    outcome: "100% compliance achieved"
  },
  {
    industry: "Healthcare",
    icon: Heart,
    description: "Advising on AI diagnostic tools and patient data compliance.",
    outcome: "HIPAA-compliant deployment"
  },
  {
    industry: "Finance",
    icon: DollarSign,
    description: "Creating responsible AI frameworks for fraud detection models.",
    outcome: "Risk reduction by 40%"
  },
  {
    industry: "Technology",
    icon: Shield,
    description: "Navigating AI ethics policies for consumer-facing products.",
    outcome: "Policy framework adopted"
  }
];
