import { Globe, Target, Database, Shield, ArrowRight, CheckCircle } from "lucide-react";

interface ExpectationItem {
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
}

const expectations: ExpectationItem[] = [
  {
    title: "AI landscape",
    description: "What it means for your organization",
    icon: Globe,
    color: "bg-brand-red"
  },
  {
    title: "Your goals", 
    description: "How AI can (and can't) achieve them",
    icon: Target,
    color: "bg-brand-purple"
  },
  {
    title: "Readiness",
    description: "Is your data and infrastructure prepared", 
    icon: Database,
    color: "bg-brand-navy"
  },
  {
    title: "Responsible AI",
    description: "Policies and processes to accelerate innovation",
    icon: Shield,
    color: "bg-brand-teal"
  },
  {
    title: "Next steps",
    description: "A clear plan forward",
    icon: ArrowRight,
    color: "bg-brand-orange"
  }
];

// Clean icon-based version inspired by the image
export function ExpectationsIconVersion() {
  return (
    <section className="py-20 px-4 bg-slate-50">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl lg:text-4xl mb-4 text-brand-navy text-center">
          What you can expect
        </h2>
        <p className="text-lg text-brand-teal text-center mb-16">
          You'll walk away with clarity and confidence in:
        </p>
        
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            {expectations.map((expectation, index) => (
              <div key={index} className="flex items-start gap-6">
                <div className={`${expectation.color} p-4 rounded-full flex-shrink-0`}>
                  <expectation.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl text-brand-navy mb-2">
                    {expectation.title}
                  </h3>
                  <p className="text-base text-brand-teal leading-relaxed">
                    {expectation.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-center">
            <div className="relative">
              <div className="w-80 h-96 bg-gradient-to-br from-brand-red via-brand-purple to-brand-navy rounded-3xl relative overflow-hidden shadow-lg">
                {/* Decorative elements */}
                <div className="absolute inset-6 bg-gradient-to-br from-white/10 via-transparent to-white/5 rounded-2xl">
                  <div className="absolute top-12 left-12 w-4 h-4 bg-white/30 rounded-full animate-pulse"></div>
                  <div className="absolute top-24 right-16 w-6 h-6 bg-white/20 rounded-full animate-pulse delay-300"></div>
                  <div className="absolute bottom-20 left-16 w-3 h-3 bg-white/40 rounded-full animate-pulse delay-700"></div>
                  <div className="absolute bottom-36 right-12 w-5 h-5 bg-white/25 rounded-full animate-pulse delay-500"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/15 rounded-full animate-pulse delay-1000"></div>
                </div>
                
                {/* Curved shapes */}
                <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-brand-orange/30 to-transparent rounded-b-3xl"></div>
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-brand-purple/40 to-transparent rounded-tr-3xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Horizontal card layout
export function ExpectationsCardVersion() {
  return (
    <section className="py-20 px-4 bg-slate-50">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl lg:text-4xl mb-4 text-brand-navy text-center">
          What you can expect
        </h2>
        <p className="text-lg text-brand-teal text-center mb-16">
          Our experts will help you build a clear understanding of
        </p>
        
        <div className="grid md:grid-cols-2 xl:grid-cols-5 gap-6">
          {expectations.map((expectation, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <div className={`${expectation.color} p-3 rounded-full w-fit mb-4`}>
                <expectation.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg text-brand-navy mb-2">
                {expectation.title}
              </h3>
              <p className="text-sm text-brand-teal leading-relaxed">
                {expectation.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Compact list version with better visual hierarchy
export function ExpectationsCompactVersion() {
  return (
    <section className="py-20 px-4 bg-slate-50">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl lg:text-4xl mb-4 text-brand-navy text-center">
          What you can expect
        </h2>
        <p className="text-lg text-brand-teal text-center mb-16">
          You'll walk away with clarity and confidence in:
        </p>
        
        <div className="space-y-6">
          {expectations.map((expectation, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 flex items-start gap-6 hover:shadow-md transition-shadow">
              <div className={`${expectation.color} p-3 rounded-full flex-shrink-0`}>
                <expectation.icon className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl text-brand-navy mb-2">
                  {expectation.title}
                </h3>
                <p className="text-base text-brand-teal leading-relaxed">
                  {expectation.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}