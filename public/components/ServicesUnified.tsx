import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Check<PERSON>ircle, Zap, Shield } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function ServicesUnified() {
  const services = [
    {
      title: "AI Readiness",
      subtitle: "Prepare to adopt AI with clarity and confidence.",
      description: "We meet you where you are, providing assessments, foundational education, and a strategic roadmap for responsible AI adoption.",
      href: "/ai-readiness",
      icon: CheckCircle,
      iconBg: "bg-brand-purple",
      cardBg: "bg-purple-50",
      buttonBg: "bg-brand-purple",
      buttonBorder: "border-brand-purple",
      buttonHoverText: "hover:text-brand-purple",
      buttonHoverBorder: "hover:border-brand-purple"
    },
    {
      title: "AI Builds",
      subtitle: "Smarter AI starts here.",
      description: "From concept to deployment, we deliver AI solutions that fit your business, built in-house or with trusted partners, our AI solutions are always compliant, scalable, and reliable.",
      href: "/ai-builds",
      icon: Zap,
      iconBg: "bg-brand-orange",
      cardBg: "bg-orange-50",
      buttonBg: "bg-brand-orange",
      buttonBorder: "border-brand-orange",
      buttonHoverText: "hover:text-brand-orange",
      buttonHoverBorder: "hover:border-brand-orange"
    },
    {
      title: "AI Counsel",
      subtitle: "Legal and ethical guidance for AI.",
      description: "Our team of lawyers and data scientists help you manage AI risks, shape policy, and build customized frameworks to keep you ahead of regulations.",
      href: "/ai-counsel",
      icon: Shield,
      iconBg: "bg-brand-red",
      cardBg: "bg-red-50",
      buttonBg: "bg-brand-red",
      buttonBorder: "border-brand-red",
      buttonHoverText: "hover:text-brand-red",
      buttonHoverBorder: "hover:border-brand-red"
    }
  ];

  return (
    <section id="services" className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-brand-navy">
            Guiding every step of your AI journey
          </h2>
          <p className="text-xl text-brand-teal">
            Our services empower you to adopt, build, and scale AI responsibly
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`${service.cardBg} border border-gray-200 h-full flex flex-col hover:shadow-lg hover:scale-[1.02] transition-all duration-300 group`}>
              <CardHeader className="text-center pb-4 pt-8">
                <div className="flex items-center justify-center mb-6">
                  <div className={`${service.iconBg} rounded-2xl p-4 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300`}>
                    <service.icon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold mb-4 text-brand-navy">
                  {service.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col px-8 pb-8">
                <p className="text-brand-navy mb-4 leading-relaxed font-medium">
                  {service.subtitle}
                </p>
                
                <p className="text-brand-teal mb-8 leading-relaxed flex-1">
                  {service.description}
                </p>
                
                <div className="mt-auto">
                  <Link 
                    to={service.href}
                    onClick={() => {
                      setTimeout(() => window.scrollTo(0, 0), 0);
                    }}
                  >
                    <Button 
                      className={`w-full ${service.buttonBg} border-2 ${service.buttonBorder} hover:bg-white ${service.buttonHoverText} ${service.buttonHoverBorder} transition-all duration-300 text-white`}
                    >
                      Learn more
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}