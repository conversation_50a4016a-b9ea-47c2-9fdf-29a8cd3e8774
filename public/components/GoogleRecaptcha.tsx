import React, { useState, FC, useCallback, useEffect } from 'react';
import { useGoogleReCaptcha } from './use-google-recaptcha';
interface GoogleRecaptchaProps {
    onVerify?: (token: string) => void;
}

export const GoogleRecaptcha: FC<GoogleRecaptchaProps> = ({ onVerify }) => {
    const { executeRecaptcha } = useGoogleReCaptcha();
    const [token, setToken] = useState('');



    const captchaClickHandler = useCallback(async () => {
        if (!executeRecaptcha) return;

        const result = await executeRecaptcha('submit');
        setToken(result);
        onVerify?.(result); // send token to parent
    }, [executeRecaptcha, onVerify]);

    return (
        <div>
            <br />
            <button onClick={captchaClickHandler}>Run verify</button>
            <br />
            {token && <p>Token: {token}</p>}
        </div>
    );
};
