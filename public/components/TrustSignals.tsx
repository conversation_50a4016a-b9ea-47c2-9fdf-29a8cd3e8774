import { ImageWithFallback } from './figma/ImageWithFallback';
import walmartLogo from '/public/asset/d57aebf7642b7163a1636cbed0954e70c53b81ae.png';
import larryMillerLogo from '/public/asset/ccf95b5a7535717f360d815736e379f55de0755c.png';
import kpmgLogo from '/public/asset/9732f0fbf666ca9738296f3bdd52dfdfb8a72580.png';
import forbesLogo from '/public/asset/5e8c8688032572cc12421fdb9e29d2a6b77d192b.png';
import nytLogo from '/public/asset/3f343ffc604a6c839e31d3f052b78abfa34ab588.png';
import fortuneLogo from '/public/asset/aff38973f76fa2a30f1e682d6ce70c05d235afcf.png';
import lawComLogo from '/public/asset/f898cbfa668171b0ab6c920c1523277ece33b354.png';
import bloomberg<PERSON>aw<PERSON><PERSON> from '/public/asset/3dfd9f1f15bd0d0f094c59ef1ae78374e55a5ee9.png';
import grantThorntonLogo from '/public/asset/c132f2f79255402fd8afa5b8aaeb7141b71e2c66.png';

import methodLogo from '/public/asset/9590f62d2fd19db1d3cd7dc7304067b7c0a13ac7.png';
import abaJournalLogo from '/public/asset/4243f8f3920efda829d7cbb8bd4317648c612c70.png';
import familyDollarLogo from '/public/asset/ca99358a9753f48fa22c75351a4091b58c31c0ba.png';
import economistLogo from '/public/asset/4597ba5eb9ede918cb70606a31735a57d00d9e0e.png';
import wsjLogo from '/public/asset/bed7c54a7ec8d32170373e2a2b3916ff9e887be2.png';
import washingtonPostLogo from '/public/asset/cc6e5572410af2f67bde71e39dd2d3cf79e63e11.png';
import hbrLogo from '/public/asset/fb1e0a04806ba8394fa01896e664f6002413c023.png';

export function TrustSignals() {
  const logos = [
    { src: walmartLogo, alt: "Walmart", name: "walmart", size: "extra-large" },
    { src: larryMillerLogo, alt: "Larry H. Miller Company", name: "larry-miller", size: "extra-large" },
    { src: kpmgLogo, alt: "KPMG", name: "kpmg", size: "default" },
    { src: grantThorntonLogo, alt: "Grant Thornton", name: "grant-thornton", size: "extra-large" },
    { src: methodLogo, alt: "Method", name: "method", size: "large" },
    { src: familyDollarLogo, alt: "Family Dollar", name: "family-dollar", size: "default" }
  ];

  const getLogoContainerClasses = (size: string) => {
    // All containers have the same height for alignment, but different widths for logo sizing
    const baseHeight = "h-16 md:h-20 lg:h-24"; // Use the largest height for all containers

    switch (size) {
      case 'extra-large':
        return `w-32 md:w-40 lg:w-48 ${baseHeight}`;
      case 'large':
        return `w-24 md:w-28 lg:w-32 ${baseHeight}`;
      default:
        return `w-20 md:w-24 lg:w-28 ${baseHeight}`;
    }
  };

  return (
    <section className="py-12 bg-white overflow-hidden">
      <div className="max-w-6xl mx-auto px-4">
        <p className="text-center text-brand-teal text-lg font-medium mb-8">
          Trusted by leading enterprises
        </p>

        {/* Scrolling container */}
        <div className="relative">
          <div className="flex animate-scroll-left">
            {/* First set of logos */}
            {logos.map((logo, index) => (
              <div
                key={`first-${logo.name}-${index}`}
                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
              >
                <div className={`${getLogoContainerClasses(logo.size)} flex items-center justify-center`}>
                  <ImageWithFallback
                    src={logo.src}
                    alt={logo.alt}
                    className="w-full h-full object-contain hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {logos.map((logo, index) => (
              <div
                key={`second-${logo.name}-${index}`}
                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
              >
                <div className={`${getLogoContainerClasses(logo.size)} flex items-center justify-center`}>
                  <ImageWithFallback
                    src={logo.src}
                    alt={logo.alt}
                    className="w-full h-full object-contain hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export function QuotedPublishedIn() {
  const logos = [
    { src: forbesLogo, alt: "Forbes", name: "forbes", size: "default" },
    { src: nytLogo, alt: "The New York Times", name: "nyt", size: "large" },
    { src: fortuneLogo, alt: "Fortune", name: "fortune", size: "default" },
    { src: lawComLogo, alt: "Law.com", name: "law-com", size: "default" },
    { src: bloombergLawLogo, alt: "Bloomberg Law", name: "bloomberg-law", size: "default" },
    { src: abaJournalLogo, alt: "ABA Journal", name: "aba-journal", size: "default" },
    { src: economistLogo, alt: "The Economist", name: "economist", size: "default" },
    { src: wsjLogo, alt: "Wall Street Journal", name: "wsj", size: "small" },
    { src: washingtonPostLogo, alt: "The Washington Post", name: "washington-post", size: "large" },
    { src: hbrLogo, alt: "Harvard Business Review", name: "hbr", size: "default" }
  ];

  const getLogoContainerClasses = (size: string) => {
    switch (size) {
      case 'small':
        return "w-16 h-8 md:w-20 md:h-10 lg:w-24 lg:h-12";
      case 'large':
        return "w-24 h-12 md:w-28 md:h-14 lg:w-32 lg:h-16";
      default:
        return "w-20 h-10 md:w-24 md:h-12 lg:w-28 lg:h-14";
    }
  };

  return (
    <section className="py-12 bg-white overflow-hidden">
      <div className="max-w-6xl mx-auto px-4">
        <p className="text-center text-brand-teal text-lg font-medium mb-8">
          Quoted & Published In
        </p>

        {/* Scrolling container */}
        <div className="relative">
          <div className="flex animate-scroll-left">
            {/* First set of logos */}
            {logos.map((logo, index) => (
              <div
                key={`first-${logo.name}-${index}`}
                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
              >
                <div className={`${getLogoContainerClasses(logo.size)} flex items-center justify-center`}>
                  <ImageWithFallback
                    src={logo.src}
                    alt={logo.alt}
                    className="w-full h-full object-contain hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {logos.map((logo, index) => (
              <div
                key={`second-${logo.name}-${index}`}
                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
              >
                <div className={`${getLogoContainerClasses(logo.size)} flex items-center justify-center`}>
                  <ImageWithFallback
                    src={logo.src}
                    alt={logo.alt}
                    className="w-full h-full object-contain hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
