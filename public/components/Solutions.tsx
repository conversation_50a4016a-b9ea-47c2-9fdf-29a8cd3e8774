import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Lightbulb, Hammer, Scale } from "lucide-react";

export function Solutions() {
  return (
    <section id="solutions" className="py-20 bg-brand-light-gray">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 text-brand-navy">Services</h2>
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card className="text-center border-brand-light-gray hover:border-brand-navy transition-colors flex flex-col h-full">
            <CardHeader className="flex-1">
              <div className="w-16 h-16 bg-brand-orange rounded-full flex items-center justify-center mx-auto mb-4">
                <Lightbulb className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-brand-navy">AI Readiness</CardTitle>
              <CardDescription className="text-brand-teal">
                For clients seeking to adopt AI for the first time, or in a new way.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full border-brand-purple text-brand-purple hover:bg-brand-purple hover:text-white">Learn More</Button>
            </CardContent>
          </Card>

          <Card className="text-center border-brand-light-gray hover:border-brand-navy transition-colors flex flex-col h-full">
            <CardHeader className="flex-1">
              <div className="w-16 h-16 bg-brand-purple rounded-full flex items-center justify-center mx-auto mb-4">
                <Hammer className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-brand-navy">AI Implementation</CardTitle>
              <CardDescription className="text-brand-teal">
                For clients who already have a well-scoped project, governance plan, and data resources.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full border-brand-purple text-brand-purple hover:bg-brand-purple hover:text-white">Learn More</Button>
            </CardContent>
          </Card>

          <Card className="text-center border-brand-light-gray hover:border-brand-navy transition-colors flex flex-col h-full">
            <CardHeader className="flex-1">
              <div className="w-16 h-16 bg-brand-red rounded-full flex items-center justify-center mx-auto mb-4">
                <Scale className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-brand-navy">AI Counsel</CardTitle>
              <CardDescription className="text-brand-teal">
                For clients looking for legal services related to AI, like testing, general guidance, or more complex governance plans.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full border-brand-purple text-brand-purple hover:bg-brand-purple hover:text-white">Learn More</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}