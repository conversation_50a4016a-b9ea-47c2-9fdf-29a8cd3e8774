export function ProcessFinal() {
  const processSteps = [
    {
      title: "Educate",
      description: "We offer AI education for leaders and teams, covering what AI is, where it creates value, and how it can introduce risk. This creates alignment and trust while preparing your organization to make informed decisions."
    },
    {
      title: "Assess", 
      description: "We facilitate a holistic evaluation of your organization. We conduct interviews to better understand your aspirations, pain points, and business goals; analyze your technical infrastructure; thoroughly assess desired use cases; and consider your existing processes. These insights ensure that our recommendations are specific, realistic, and actionable."
    },
    {
      title: "Deliver",
      description: "We deliver a comprehensive <strong>AI Opportunity Roadmap</strong> summarizing findings, identifying opportunities, and providing clear recommendations."
    }
  ];

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl mb-6 text-brand-navy">
            Our process
          </h2>
        </div>
        
        <div className="space-y-12">
          {processSteps.map((step, index) => (
            <div key={index} className="border-l-4 border-brand-red pl-8 py-4">
              <div className="flex items-start gap-4">
                <span className="text-3xl font-bold text-brand-red mt-1">
                  {String(index + 1).padStart(2, '0')}
                </span>
                <div className="flex-1">
                  <h4 className="text-2xl text-brand-navy mb-3">{step.title}</h4>
                  <p 
                    className="text-brand-teal leading-relaxed text-lg"
                    dangerouslySetInnerHTML={{ __html: step.description }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}