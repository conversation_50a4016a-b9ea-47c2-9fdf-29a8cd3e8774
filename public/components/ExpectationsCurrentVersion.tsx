import { CheckCircle } from "lucide-react";
import { expectationsOriginal } from './ExpectationsConstants';
import exampleImage from '/public/asset/1915abda9480f132f89dfe7fe016661be1e73cf6.png';

export function ExpectationsCurrentVersion() {
  return (
    <section className="py-20 px-4 bg-slate-50">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl lg:text-4xl mb-12 text-brand-navy text-center">
          What you can expect
        </h2>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-lg text-brand-teal leading-relaxed">
            <p className="mb-6">Our experts will help you build a clear understanding of</p>
            <ul className="space-y-4">
              {expectationsOriginal.map((expectation, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-brand-red mt-1 mr-4 flex-shrink-0" />
                  <span>{expectation}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="flex justify-center">
            <img
              src={exampleImage}
              alt="Reference image"
              className="w-full max-w-md h-auto"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
