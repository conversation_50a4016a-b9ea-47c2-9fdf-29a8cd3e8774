import { <PERSON><PERSON> } from "./ui/button";
import { BookOpen, Scale, Heart, Gavel } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import communityLogo from '/public/asset/d67c2e6226ab6ab0de53ee737b22a531fbfccdc1.png';

export function ClarionCommunity() {
  const initiatives = [
    {
      icon: BookOpen,
      title: "Education & research",
      description: "Building AI learning programs.",
      color: "bg-community-teal"
    },
    {
      icon: Scale,
      title: "Policy & governance",
      description: "Shaping ethical AI regulations.",
      color: "bg-community-purple"
    },
    {
      icon: Heart,
      title: "Human rights",
      description: "Preventing misuse and protecting rights.",
      color: "bg-community-coral"
    },
    {
      icon: Gavel,
      title: "Access to justice",
      description: "Supporting justice through AI expertise.",
      color: "bg-community-sky-blue"
    }
  ];

  return (
    <section id="community" className="relative py-20 bg-gradient-to-br from-community-sky-blue/10 via-white to-community-neutral-gray/20">
      {/* Decorative gradient line separator */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00B2A9] via-[#802788] to-[#00B2A9] z-10"></div>
      <div className="max-w-4xl mx-auto px-4">
        {/* Header with Logo */}
        <div className="text-center mb-16">
          <img
            src={communityLogo}
            alt="Clarion Community - AI for Good, AI for All"
            className="h-16 md:h-20 mx-auto mb-8"
          />
          <div className="mb-6">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 pb-2 bg-gradient-to-r from-community-navy via-community-purple to-community-teal bg-clip-text text-transparent">
              Our commitment to AI for good
            </h2>
          </div>
          <div>
            <p className="text-lg md:text-xl text-community-dark-blue leading-relaxed">
              The Clarion Academy advances the safe, fair, and effective use of AI. We bring together leaders from business, government, academia, the judiciary, and human rights organizations to ensure this transformative technology benefits society responsibly. Through research, guidance, and global partnerships, the Academy supports non-profits, educational institutions, and governments. Together, our community promotes justice, safeguards human rights, and prevents exploitation in the age of AI.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4">
        {/* Initiatives Section */}
        <div className="mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-center mb-12 text-community-navy">Our initiatives in responsible AI</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {initiatives.map((initiative, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-community-neutral-gray/30 hover:border-community-teal/30"
              >
                <div className={`w-12 h-12 ${initiative.color} rounded-lg flex items-center justify-center mb-4 shadow-md`}>
                  <initiative.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold mb-3 text-community-navy">
                  {initiative.title}
                </h4>
                <p className="text-community-dark-blue text-sm leading-relaxed">
                  {initiative.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Link to="/academy">
            <Button
              size="lg"
              className="bg-community-teal border border-community-teal hover:bg-white hover:text-community-teal hover:border-community-teal text-white px-8 py-4 text-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Join the community
            </Button>
          </Link>
        </div>
      </div>

      {/* Decorative gradient line separator at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00B2A9] via-[#802788] to-[#00B2A9] z-10"></div>
    </section>
  );
}
