import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "./ui/accordion";
import { Scale, Shield, BookOpen, Gavel, Globe } from "lucide-react";

export function DifferentiatorsAccordion() {
  const differentiators = [
    {
      icon: Scale,
      title: "Lawyers who are also data scientists",
      description: "Our leadership combines legal experience with technical knowledge. We speak both languages fluently: compliance and code.",
      iconColor: "text-brand-red",
      bgColor: "bg-brand-red/10"
    },
    {
      icon: Shield,
      title: "Governance built in",
      description: "We integrate legal, ethical, and policy safeguards into AI systems from the start. That way, you can innovate without compromising trust.",
      iconColor: "text-brand-purple",
      bgColor: "bg-brand-purple/10"
    },
    {
      icon: BookOpen,
      title: "Policy insight from the inside",
      description: "Our team includes former government advisors, including from the FTC. We understand how regulators think because we have been regulators ourselves.",
      iconColor: "text-brand-teal",
      bgColor: "bg-brand-teal/10"
    },
    {
      icon: Gavel,
      title: "We help shape policy, not just respond to it",
      description: "From advising the White House to contributing to national legislation, we are directly involved in shaping the future of AI regulation.",
      iconColor: "text-brand-orange",
      bgColor: "bg-brand-orange/10"
    },
    {
      icon: Globe,
      title: "Cross-functional and cross-industry",
      description: "We support clients in technology, finance, healthcare, manufacturing, public service, and beyond. Our work is tailored to the unique risks and realities of each field.",
      iconColor: "text-brand-navy",
      bgColor: "bg-brand-navy/10"
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl mb-4 text-brand-navy">
            Why organizations trust Clarion
          </h2>
          <p className="text-xl text-brand-teal">
            AI is moving fast. We help you move forward without losing control.
          </p>
        </div>
        
        <Accordion type="multiple" className="space-y-4">
          {differentiators.map((item, index) => (
            <AccordionItem 
              key={index} 
              value={`item-${index}`} 
              className="bg-white rounded-lg border-2 border-slate-200 hover:border-brand-red transition-colors"
            >
              <AccordionTrigger className="px-6 py-4 hover:no-underline group">
                <div className="flex items-center">
                  <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center mr-4 group-hover:bg-brand-red transition-colors`}>
                    <item.icon className={`h-5 w-5 ${item.iconColor} group-hover:text-white transition-colors`} />
                  </div>
                  <span className="text-lg text-brand-navy group-hover:text-brand-red transition-colors text-left">
                    {item.title}
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-6 pb-4">
                <p className="text-brand-teal leading-relaxed ml-14">
                  {item.description}
                </p>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}