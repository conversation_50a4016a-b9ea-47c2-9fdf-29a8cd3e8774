export function ValueStatementsScroll() {
  const statements = [
    "We delivered an AI roadmap that a $12B industrial firm's board unanimously approved for enterprise rollout.",
    "Our structured approach transformed AI from an IT experiment into a board-endorsed growth strategy.",
    "We turned AI skepticism into support by showing early ROI, paving the way for enterprise-wide adoption at a multinational manufacturer.",
    "Our AI governance framework gave a $500B+ retailer board-ready confidence in deploying customer-facing AI tools.",
    "We helped a global logistics company redesign its AI pilot strategy, reducing redundant spend and saving $18M annually."
  ];

  return (
    <section className="py-16 bg-brand-light-gray overflow-hidden">
      <div className="max-w-6xl mx-auto px-4">
        <p className="text-center text-brand-teal text-lg font-medium mb-8">
          Proven results
        </p>

        {/* Scrolling container */}
        <div className="relative">
          <div className="flex center">
            {/* First set of statements */}
            {statements.map((statement, index) => (
              <div
                key={`first-${index}`}
                className="flex-shrink-0 mx-6 md:mx-8 lg:mx-10"
              >
                <div className="w-80 md:w-96 lg:w-[28rem] bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                  <blockquote className="text-brand-teal text-sm md:text-base leading-relaxed">
                    {statement}
                  </blockquote>
                </div>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {statements.map((statement, index) => (
              <div
                key={`second-${index}`}
                className="flex-shrink-0 mx-6 md:mx-8 lg:mx-10"
              >
                <div className="w-80 md:w-96 lg:w-[28rem] bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                  <blockquote className="text-brand-teal text-sm md:text-base leading-relaxed">
                    {statement}
                  </blockquote>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
