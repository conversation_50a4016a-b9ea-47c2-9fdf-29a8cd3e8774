@import url("https://fonts.googleapis.com/css2?family=Area:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Stolzl:wght@300;400;500;600;700&display=swap");
/*! tailwindcss v4.1.3 | MIT License | https://tailwindcss.com */
@layer properties {
    @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
        *, :before, :after, ::backdrop {
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-translate-z: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-scale-z: 1;
            --tw-rotate-x: rotateX(0);
            --tw-rotate-y: rotateY(0);
            --tw-rotate-z: rotateZ(0);
            --tw-skew-x: skewX(0);
            --tw-skew-y: skewY(0);
            --tw-space-y-reverse: 0;
            --tw-space-x-reverse: 0;
            --tw-border-style: solid;
            --tw-gradient-position: initial;
            --tw-gradient-from: #0000;
            --tw-gradient-via: #0000;
            --tw-gradient-to: #0000;
            --tw-gradient-stops: initial;
            --tw-gradient-via-stops: initial;
            --tw-gradient-from-position: 0%;
            --tw-gradient-via-position: 50%;
            --tw-gradient-to-position: 100%;
            --tw-leading: initial;
            --tw-font-weight: initial;
            --tw-tracking: initial;
            --tw-ordinal: initial;
            --tw-slashed-zero: initial;
            --tw-numeric-figure: initial;
            --tw-numeric-spacing: initial;
            --tw-numeric-fraction: initial;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-color: initial;
            --tw-shadow-alpha: 100%;
            --tw-inset-shadow: 0 0 #0000;
            --tw-inset-shadow-color: initial;
            --tw-inset-shadow-alpha: 100%;
            --tw-ring-color: initial;
            --tw-ring-shadow: 0 0 #0000;
            --tw-inset-ring-color: initial;
            --tw-inset-ring-shadow: 0 0 #0000;
            --tw-ring-inset: initial;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-outline-style: solid;
            --tw-blur: initial;
            --tw-brightness: initial;
            --tw-contrast: initial;
            --tw-grayscale: initial;
            --tw-hue-rotate: initial;
            --tw-invert: initial;
            --tw-opacity: initial;
            --tw-saturate: initial;
            --tw-sepia: initial;
            --tw-drop-shadow: initial;
            --tw-drop-shadow-color: initial;
            --tw-drop-shadow-alpha: 100%;
            --tw-drop-shadow-size: initial;
            --tw-backdrop-blur: initial;
            --tw-backdrop-brightness: initial;
            --tw-backdrop-contrast: initial;
            --tw-backdrop-grayscale: initial;
            --tw-backdrop-hue-rotate: initial;
            --tw-backdrop-invert: initial;
            --tw-backdrop-opacity: initial;
            --tw-backdrop-saturate: initial;
            --tw-backdrop-sepia: initial;
            --tw-duration: initial;
            --tw-ease: initial;
            --tw-content: "";
        }
    }
}

@layer theme {
    :root, :host {
        --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        --color-red-50: oklch(.971 .013 17.38);
        --color-orange-50: oklch(.98 .016 73.684);
        --color-purple-50: oklch(.977 .014 308.299);
        --color-slate-50: oklch(.984 .003 247.858);
        --color-slate-200: oklch(.929 .013 255.508);
        --color-gray-100: oklch(.967 .003 264.542);
        --color-gray-200: oklch(.928 .006 264.531);
        --color-gray-600: oklch(.446 .03 256.802);
        --color-gray-700: oklch(.373 .034 259.733);
        --color-gray-800: oklch(.278 .033 256.848);
        --color-black: #000;
        --color-white: #fff;
        --spacing: .25rem;
        --container-sm: 24rem;
        --container-md: 28rem;
        --container-lg: 32rem;
        --container-2xl: 42rem;
        --container-3xl: 48rem;
        --container-4xl: 56rem;
        --container-6xl: 72rem;
        --container-7xl: 80rem;
        --text-xs: .75rem;
        --text-xs--line-height: calc(1 / .75);
        --text-sm: .875rem;
        --text-sm--line-height: calc(1.25 / .875);
        --text-base: 1rem;
        --text-base--line-height: calc(1.5 / 1);
        --text-lg: 1.125rem;
        --text-lg--line-height: calc(1.75 / 1.125);
        --text-xl: 1.25rem;
        --text-xl--line-height: calc(1.75 / 1.25);
        --text-2xl: 1.5rem;
        --text-2xl--line-height: calc(2 / 1.5);
        --text-3xl: 1.875rem;
        --text-3xl--line-height: calc(2.25 / 1.875);
        --text-4xl: 2.25rem;
        --text-4xl--line-height: calc(2.5 / 2.25);
        --text-5xl: 3rem;
        --text-5xl--line-height: 1;
        --text-6xl: 3.75rem;
        --text-6xl--line-height: 1;
        --font-weight-normal: 400;
        --font-weight-medium: 500;
        --font-weight-semibold: 600;
        --font-weight-bold: 700;
        --tracking-tight: -.025em;
        --tracking-wider: .05em;
        --tracking-widest: .1em;
        --leading-tight: 1.25;
        --leading-relaxed: 1.625;
        --radius-xs: .125rem;
        --radius-2xl: 1rem;
        --radius-3xl: 1.5rem;
        --ease-in-out: cubic-bezier(.4, 0, .2, 1);
        --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
        --blur-sm: 8px;
        --aspect-video: 16 / 9;
        --default-transition-duration: .15s;
        --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
        --default-font-family: var(--font-sans);
        --default-font-feature-settings: var(--font-sans--font-feature-settings);
        --default-font-variation-settings: var(--font-sans--font-variation-settings);
        --default-mono-font-family: var(--font-mono);
        --default-mono-font-feature-settings: var(--font-mono--font-feature-settings);
        --default-mono-font-variation-settings: var(--font-mono--font-variation-settings);
        --color-border: var(--border);
        --color-brand-navy: var(--brand-navy);
        --color-brand-red: var(--brand-red);
        --color-brand-light-gray: var(--brand-light-gray);
        --color-brand-purple: var(--brand-purple);
        --color-brand-orange: var(--brand-orange);
        --color-brand-teal: var(--brand-teal);
        --color-community-teal: var(--community-teal);
        --color-community-purple: var(--community-purple);
        --color-community-coral: var(--community-coral);
        --color-community-sky-blue: var(--community-sky-blue);
        --color-community-golden-yellow: var(--community-golden-yellow);
        --color-community-navy: var(--community-navy);
        --color-community-neutral-gray: var(--community-neutral-gray);
        --color-community-dark-blue: var(--community-dark-blue);
        --color-academy-teal: var(--academy-teal);
        --color-academy-purple: var(--academy-purple);
    }
}

@layer base {
    *, :after, :before, ::backdrop {
        box-sizing: border-box;
        border: 0 solid;
        margin: 0;
        padding: 0;
    }

    ::file-selector-button {
        box-sizing: border-box;
        border: 0 solid;
        margin: 0;
        padding: 0;
    }

    html, :host {
        -webkit-text-size-adjust: 100%;
        tab-size: 4;
        line-height: 1.5;
        font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
        font-feature-settings: var(--default-font-feature-settings, normal);
        font-variation-settings: var(--default-font-variation-settings, normal);
        -webkit-tap-highlight-color: transparent;
    }

    body {
        line-height: inherit;
    }

    hr {
        height: 0;
        color: inherit;
        border-top-width: 1px;
    }

    abbr:where([title]) {
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
    }

    h1, h2, h3, h4, h5, h6 {
        font-size: inherit;
        font-weight: inherit;
    }

    a {
        color: inherit;
        -webkit-text-decoration: inherit;
        -webkit-text-decoration: inherit;
        text-decoration: inherit;
    }

    b, strong {
        font-weight: bolder;
    }

    code, kbd, samp, pre {
        font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
        font-feature-settings: var(--default-mono-font-feature-settings, normal);
        font-variation-settings: var(--default-mono-font-variation-settings, normal);
        font-size: 1em;
    }

    small {
        font-size: 80%;
    }

    sub, sup {
        vertical-align: baseline;
        font-size: 75%;
        line-height: 0;
        position: relative;
    }

    sub {
        bottom: -.25em;
    }

    sup {
        top: -.5em;
    }

    table {
        text-indent: 0;
        border-color: inherit;
        border-collapse: collapse;
    }

    :-moz-focusring {
        outline: auto;
    }

    progress {
        vertical-align: baseline;
    }

    summary {
        display: list-item;
    }

    ol, ul, menu {
        list-style: none;
    }

    img, svg, video, canvas, audio, iframe, embed, object {
        vertical-align: middle;
        display: block;
    }

    img, video {
        max-width: 100%;
        height: auto;
    }

    button, input, select, optgroup, textarea {
        font: inherit;
        font-feature-settings: inherit;
        font-variation-settings: inherit;
        letter-spacing: inherit;
        color: inherit;
        opacity: 1;
        background-color: #0000;
        border-radius: 0;
    }

    ::file-selector-button {
        font: inherit;
        font-feature-settings: inherit;
        font-variation-settings: inherit;
        letter-spacing: inherit;
        color: inherit;
        opacity: 1;
        background-color: #0000;
        border-radius: 0;
    }

    :where(select:is([multiple], [size])) optgroup {
        font-weight: bolder;
    }

    :where(select:is([multiple], [size])) optgroup option {
        padding-inline-start: 20px;
    }

    ::file-selector-button {
        margin-inline-end: 4px;
    }

    ::placeholder {
        opacity: 1;
        color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
        ::placeholder {
            color: color-mix(in oklab, currentColor 50%, transparent);
        }
    }

    textarea {
        resize: vertical;
    }

    ::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    ::-webkit-date-and-time-value {
        min-height: 1lh;
        text-align: inherit;
    }

    ::-webkit-datetime-edit {
        display: inline-flex;
    }

    ::-webkit-datetime-edit-fields-wrapper {
        padding: 0;
    }

    ::-webkit-datetime-edit {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-year-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-month-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-day-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-hour-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-minute-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-second-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-millisecond-field {
        padding-block: 0;
    }

    ::-webkit-datetime-edit-meridiem-field {
        padding-block: 0;
    }

    :-moz-ui-invalid {
        box-shadow: none;
    }

    button, input:where([type="button"], [type="reset"], [type="submit"]) {
        appearance: button;
    }

    ::file-selector-button {
        appearance: button;
    }

    ::-webkit-inner-spin-button {
        height: auto;
    }

    ::-webkit-outer-spin-button {
        height: auto;
    }

    [hidden]:where(:not([hidden="until-found"])) {
        display: none !important;
    }

    * {
        border-color: var(--border);
        outline-color: var(--ring);
    }

    @supports (color: color-mix(in lab, red, red)) {
        * {
            outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
        }
    }

    body {
        background-color: var(--background);
        color: var(--foreground);
    }

    * {
        border-color: var(--border);
        outline-color: var(--ring);
    }

    @supports (color: color-mix(in lab, red, red)) {
        * {
            outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
        }
    }

    body {
        background-color: var(--background);
        color: var(--foreground);
        font-family: Area, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) h1 {
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-medium);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) h2 {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-medium);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) h3 {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-medium);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) h4 {
        font-size: var(--text-base);
        font-weight: var(--font-weight-medium);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) p {
        font-size: var(--text-base);
        font-weight: var(--font-weight-normal);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) label, :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) button {
        font-size: var(--text-base);
        font-weight: var(--font-weight-medium);
        line-height: 1.5;
    }

    :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) input {
        font-size: var(--text-base);
        font-weight: var(--font-weight-normal);
        line-height: 1.5;
    }
}

@layer utilities {
    .\@container\/card-header {
        container: card-header / inline-size;
    }

    .pointer-events-none {
        pointer-events: none;
    }

    .invisible {
        visibility: hidden;
    }

    .visible {
        visibility: visible;
    }

    .sr-only {
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
        width: 1px;
        height: 1px;
        margin: -1px;
        padding: 0;
        position: absolute;
        overflow: hidden;
    }

    .absolute {
        position: absolute;
    }

    .fixed {
        position: fixed;
    }

    .relative {
        position: relative;
    }

    .sticky {
        position: sticky;
    }

    .inset-0 {
        inset: calc(var(--spacing) * 0);
    }

    .inset-6 {
        inset: calc(var(--spacing) * 6);
    }

    .inset-x-0 {
        inset-inline: calc(var(--spacing) * 0);
    }

    .inset-y-0 {
        inset-block: calc(var(--spacing) * 0);
    }

    .-top-6 {
        top: calc(var(--spacing) * -6);
    }

    .-top-12 {
        top: calc(var(--spacing) * -12);
    }

    .top-0 {
        top: calc(var(--spacing) * 0);
    }

    .top-1\.5 {
        top: calc(var(--spacing) * 1.5);
    }

    .top-1\/2 {
        top: 50%;
    }

    .top-3\.5 {
        top: calc(var(--spacing) * 3.5);
    }

    .top-4 {
        top: calc(var(--spacing) * 4);
    }

    .top-12 {
        top: calc(var(--spacing) * 12);
    }

    .top-16 {
        top: calc(var(--spacing) * 16);
    }

    .top-24 {
        top: calc(var(--spacing) * 24);
    }

    .top-\[1px\] {
        top: 1px;
    }

    .top-\[50\%\] {
        top: 50%;
    }

    .top-\[60\%\] {
        top: 60%;
    }

    .top-full {
        top: 100%;
    }

    .-right-1 {
        right: calc(var(--spacing) * -1);
    }

    .-right-12 {
        right: calc(var(--spacing) * -12);
    }

    .right-0 {
        right: calc(var(--spacing) * 0);
    }

    .right-1 {
        right: calc(var(--spacing) * 1);
    }

    .right-2 {
        right: calc(var(--spacing) * 2);
    }

    .right-3 {
        right: calc(var(--spacing) * 3);
    }

    .right-4 {
        right: calc(var(--spacing) * 4);
    }

    .right-12 {
        right: calc(var(--spacing) * 12);
    }

    .right-16 {
        right: calc(var(--spacing) * 16);
    }

    .-bottom-12 {
        bottom: calc(var(--spacing) * -12);
    }

    .bottom-0 {
        bottom: calc(var(--spacing) * 0);
    }

    .bottom-20 {
        bottom: calc(var(--spacing) * 20);
    }

    .bottom-36 {
        bottom: calc(var(--spacing) * 36);
    }

    .-left-12 {
        left: calc(var(--spacing) * -12);
    }

    .left-0 {
        left: calc(var(--spacing) * 0);
    }

    .left-1 {
        left: calc(var(--spacing) * 1);
    }

    .left-1\/2 {
        left: 50%;
    }

    .left-2 {
        left: calc(var(--spacing) * 2);
    }

    .left-6 {
        left: calc(var(--spacing) * 6);
    }

    .left-8 {
        left: calc(var(--spacing) * 8);
    }

    .left-12 {
        left: calc(var(--spacing) * 12);
    }

    .left-16 {
        left: calc(var(--spacing) * 16);
    }

    .left-\[50\%\] {
        left: 50%;
    }

    .isolate {
        isolation: isolate;
    }

    .z-10 {
        z-index: 10;
    }

    .z-20 {
        z-index: 20;
    }

    .z-50 {
        z-index: 50;
    }

    .z-\[1\] {
        z-index: 1;
    }

    .col-start-2 {
        grid-column-start: 2;
    }

    .row-span-2 {
        grid-row: span 2 / span 2;
    }

    .row-start-1 {
        grid-row-start: 1;
    }

    .container {
        width: 100%;
    }

    @media (width >= 40rem) {
        .container {
            max-width: 40rem;
        }
    }

    @media (width >= 48rem) {
        .container {
            max-width: 48rem;
        }
    }

    @media (width >= 64rem) {
        .container {
            max-width: 64rem;
        }
    }

    @media (width >= 80rem) {
        .container {
            max-width: 80rem;
        }
    }

    @media (width >= 96rem) {
        .container {
            max-width: 96rem;
        }
    }

    .-mx-1 {
        margin-inline: calc(var(--spacing) * -1);
    }

    .mx-2 {
        margin-inline: calc(var(--spacing) * 2);
    }

    .mx-3\.5 {
        margin-inline: calc(var(--spacing) * 3.5);
    }

    .mx-6 {
        margin-inline: calc(var(--spacing) * 6);
    }

    .mx-8 {
        margin-inline: calc(var(--spacing) * 8);
    }

    .mx-auto {
        margin-inline: auto;
    }

    .my-0\.5 {
        margin-block: calc(var(--spacing) * .5);
    }

    .my-1 {
        margin-block: calc(var(--spacing) * 1);
    }

    .my-8 {
        margin-block: calc(var(--spacing) * 8);
    }

    .-mt-4 {
        margin-top: calc(var(--spacing) * -4);
    }

    .mt-0\.5 {
        margin-top: calc(var(--spacing) * .5);
    }

    .mt-1 {
        margin-top: calc(var(--spacing) * 1);
    }

    .mt-1\.5 {
        margin-top: calc(var(--spacing) * 1.5);
    }

    .mt-2 {
        margin-top: calc(var(--spacing) * 2);
    }

    .mt-3 {
        margin-top: calc(var(--spacing) * 3);
    }

    .mt-4 {
        margin-top: calc(var(--spacing) * 4);
    }

    .mt-6 {
        margin-top: calc(var(--spacing) * 6);
    }

    .mt-8 {
        margin-top: calc(var(--spacing) * 8);
    }

    .mt-12 {
        margin-top: calc(var(--spacing) * 12);
    }

    .mt-16 {
        margin-top: calc(var(--spacing) * 16);
    }

    .mt-auto {
        margin-top: auto;
    }

    .mr-2 {
        margin-right: calc(var(--spacing) * 2);
    }

    .mr-3 {
        margin-right: calc(var(--spacing) * 3);
    }

    .mr-4 {
        margin-right: calc(var(--spacing) * 4);
    }

    .mr-6 {
        margin-right: calc(var(--spacing) * 6);
    }

    .mb-1 {
        margin-bottom: calc(var(--spacing) * 1);
    }

    .mb-2 {
        margin-bottom: calc(var(--spacing) * 2);
    }

    .mb-3 {
        margin-bottom: calc(var(--spacing) * 3);
    }

    .mb-4 {
        margin-bottom: calc(var(--spacing) * 4);
    }

    .mb-6 {
        margin-bottom: calc(var(--spacing) * 6);
    }

    .mb-8 {
        margin-bottom: calc(var(--spacing) * 8);
    }

    .mb-10 {
        margin-bottom: calc(var(--spacing) * 10);
    }

    .mb-12 {
        margin-bottom: calc(var(--spacing) * 12);
    }

    .mb-16 {
        margin-bottom: calc(var(--spacing) * 16);
    }

    .mb-20 {
        margin-bottom: calc(var(--spacing) * 20);
    }

    .-ml-4 {
        margin-left: calc(var(--spacing) * -4);
    }

    .ml-1 {
        margin-left: calc(var(--spacing) * 1);
    }

    .ml-2 {
        margin-left: calc(var(--spacing) * 2);
    }

    .ml-4 {
        margin-left: calc(var(--spacing) * 4);
    }

    .ml-6 {
        margin-left: calc(var(--spacing) * 6);
    }

    .ml-14 {
        margin-left: calc(var(--spacing) * 14);
    }

    .ml-16 {
        margin-left: calc(var(--spacing) * 16);
    }

    .ml-auto {
        margin-left: auto;
    }

    .line-clamp-1 {
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
    }

    .block {
        display: block;
    }

    .flex {
        display: flex;
    }

    .grid {
        display: grid;
    }

    .hidden {
        display: none;
    }

    .inline-block {
        display: inline-block;
    }

    .inline-flex {
        display: inline-flex;
    }

    .table {
        display: table;
    }

    .table-caption {
        display: table-caption;
    }

    .table-cell {
        display: table-cell;
    }

    .table-row {
        display: table-row;
    }

    .field-sizing-content {
        field-sizing: content;
    }

    .aspect-square {
        aspect-ratio: 1;
    }

    .aspect-video {
        aspect-ratio: var(--aspect-video);
    }

    .size-2 {
        width: calc(var(--spacing) * 2);
        height: calc(var(--spacing) * 2);
    }

    .size-2\.5 {
        width: calc(var(--spacing) * 2.5);
        height: calc(var(--spacing) * 2.5);
    }

    .size-3 {
        width: calc(var(--spacing) * 3);
        height: calc(var(--spacing) * 3);
    }

    .size-3\.5 {
        width: calc(var(--spacing) * 3.5);
        height: calc(var(--spacing) * 3.5);
    }

    .size-4 {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
    }

    .size-7 {
        width: calc(var(--spacing) * 7);
        height: calc(var(--spacing) * 7);
    }

    .size-8 {
        width: calc(var(--spacing) * 8);
        height: calc(var(--spacing) * 8);
    }

    .size-9 {
        width: calc(var(--spacing) * 9);
        height: calc(var(--spacing) * 9);
    }

    .size-10 {
        width: calc(var(--spacing) * 10);
        height: calc(var(--spacing) * 10);
    }

    .size-full {
        width: 100%;
        height: 100%;
    }

    .h-1 {
        height: calc(var(--spacing) * 1);
    }

    .h-1\.5 {
        height: calc(var(--spacing) * 1.5);
    }

    .h-2 {
        height: calc(var(--spacing) * 2);
    }

    .h-2\.5 {
        height: calc(var(--spacing) * 2.5);
    }

    .h-3 {
        height: calc(var(--spacing) * 3);
    }

    .h-4 {
        height: calc(var(--spacing) * 4);
    }

    .h-5 {
        height: calc(var(--spacing) * 5);
    }

    .h-6 {
        height: calc(var(--spacing) * 6);
    }

    .h-7 {
        height: calc(var(--spacing) * 7);
    }

    .h-8 {
        height: calc(var(--spacing) * 8);
    }

    .h-9 {
        height: calc(var(--spacing) * 9);
    }

    .h-10 {
        height: calc(var(--spacing) * 10);
    }

    .h-12 {
        height: calc(var(--spacing) * 12);
    }

    .h-16 {
        height: calc(var(--spacing) * 16);
    }

    .h-20 {
        height: calc(var(--spacing) * 20);
    }

    .h-24 {
        height: calc(var(--spacing) * 24);
    }

    .h-32 {
        height: calc(var(--spacing) * 32);
    }

    .h-64 {
        height: calc(var(--spacing) * 64);
    }

    .h-96 {
        height: calc(var(--spacing) * 96);
    }

    .h-\[1\.15rem\] {
        height: 1.15rem;
    }

    .h-\[calc\(100\%-1px\)\] {
        height: calc(100% - 1px);
    }

    .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
        height: var(--radix-navigation-menu-viewport-height);
    }

    .h-\[var\(--radix-select-trigger-height\)\] {
        height: var(--radix-select-trigger-height);
    }

    .h-auto {
        height: auto;
    }

    .h-full {
        height: 100%;
    }

    .h-px {
        height: 1px;
    }

    .h-svh {
        height: 100svh;
    }

    .max-h-\(--radix-context-menu-content-available-height\) {
        max-height: var(--radix-context-menu-content-available-height);
    }

    .max-h-\(--radix-dropdown-menu-content-available-height\) {
        max-height: var(--radix-dropdown-menu-content-available-height);
    }

    .max-h-\(--radix-select-content-available-height\) {
        max-height: var(--radix-select-content-available-height);
    }

    .max-h-\[300px\] {
        max-height: 300px;
    }

    .min-h-0 {
        min-height: calc(var(--spacing) * 0);
    }

    .min-h-4 {
        min-height: calc(var(--spacing) * 4);
    }

    .min-h-16 {
        min-height: calc(var(--spacing) * 16);
    }

    .min-h-32 {
        min-height: calc(var(--spacing) * 32);
    }

    .min-h-\[120px\] {
        min-height: 120px;
    }

    .min-h-\[200px\] {
        min-height: 200px;
    }

    .min-h-screen {
        min-height: 100vh;
    }

    .min-h-svh {
        min-height: 100svh;
    }

    .w-\(--sidebar-width\) {
        width: var(--sidebar-width);
    }

    .w-0 {
        width: calc(var(--spacing) * 0);
    }

    .w-0\.5 {
        width: calc(var(--spacing) * .5);
    }

    .w-1 {
        width: calc(var(--spacing) * 1);
    }

    .w-1\/2 {
        width: 50%;
    }

    .w-2 {
        width: calc(var(--spacing) * 2);
    }

    .w-2\.5 {
        width: calc(var(--spacing) * 2.5);
    }

    .w-3 {
        width: calc(var(--spacing) * 3);
    }

    .w-3\/4 {
        width: 75%;
    }

    .w-4 {
        width: calc(var(--spacing) * 4);
    }

    .w-5 {
        width: calc(var(--spacing) * 5);
    }

    .w-6 {
        width: calc(var(--spacing) * 6);
    }

    .w-8 {
        width: calc(var(--spacing) * 8);
    }

    .w-9 {
        width: calc(var(--spacing) * 9);
    }

    .w-10 {
        width: calc(var(--spacing) * 10);
    }

    .w-12 {
        width: calc(var(--spacing) * 12);
    }

    .w-16 {
        width: calc(var(--spacing) * 16);
    }

    .w-20 {
        width: calc(var(--spacing) * 20);
    }

    .w-24 {
        width: calc(var(--spacing) * 24);
    }

    .w-32 {
        width: calc(var(--spacing) * 32);
    }

    .w-48 {
        width: calc(var(--spacing) * 48);
    }

    .w-64 {
        width: calc(var(--spacing) * 64);
    }

    .w-72 {
        width: calc(var(--spacing) * 72);
    }

    .w-80 {
        width: calc(var(--spacing) * 80);
    }

    .w-\[100px\] {
        width: 100px;
    }

    .w-auto {
        width: auto;
    }

    .w-fit {
        width: fit-content;
    }

    .w-full {
        width: 100%;
    }

    .w-max {
        width: max-content;
    }

    .w-px {
        width: 1px;
    }

    .max-w-\(--skeleton-width\) {
        max-width: var(--skeleton-width);
    }

    .max-w-2xl {
        max-width: var(--container-2xl);
    }

    .max-w-3xl {
        max-width: var(--container-3xl);
    }

    .max-w-4xl {
        max-width: var(--container-4xl);
    }

    .max-w-6xl {
        max-width: var(--container-6xl);
    }

    .max-w-7xl {
        max-width: var(--container-7xl);
    }

    .max-w-\[calc\(100\%-2rem\)\] {
        max-width: calc(100% - 2rem);
    }

    .max-w-max {
        max-width: max-content;
    }

    .max-w-md {
        max-width: var(--container-md);
    }

    .max-w-none {
        max-width: none;
    }

    .min-w-0 {
        min-width: calc(var(--spacing) * 0);
    }

    .min-w-5 {
        min-width: calc(var(--spacing) * 5);
    }

    .min-w-8 {
        min-width: calc(var(--spacing) * 8);
    }

    .min-w-9 {
        min-width: calc(var(--spacing) * 9);
    }

    .min-w-10 {
        min-width: calc(var(--spacing) * 10);
    }

    .min-w-\[8rem\] {
        min-width: 8rem;
    }

    .min-w-\[12rem\] {
        min-width: 12rem;
    }

    .min-w-\[var\(--radix-select-trigger-width\)\] {
        min-width: var(--radix-select-trigger-width);
    }

    .flex-1 {
        flex: 1;
    }

    .flex-shrink-0, .shrink-0 {
        flex-shrink: 0;
    }

    .flex-grow, .grow {
        flex-grow: 1;
    }

    .grow-0 {
        flex-grow: 0;
    }

    .basis-full {
        flex-basis: 100%;
    }

    .caption-bottom {
        caption-side: bottom;
    }

    .border-collapse {
        border-collapse: collapse;
    }

    .origin-\(--radix-context-menu-content-transform-origin\) {
        transform-origin: var(--radix-context-menu-content-transform-origin);
    }

    .origin-\(--radix-dropdown-menu-content-transform-origin\) {
        transform-origin: var(--radix-dropdown-menu-content-transform-origin);
    }

    .origin-\(--radix-hover-card-content-transform-origin\) {
        transform-origin: var(--radix-hover-card-content-transform-origin);
    }

    .origin-\(--radix-menubar-content-transform-origin\) {
        transform-origin: var(--radix-menubar-content-transform-origin);
    }

    .origin-\(--radix-popover-content-transform-origin\) {
        transform-origin: var(--radix-popover-content-transform-origin);
    }

    .origin-\(--radix-select-content-transform-origin\) {
        transform-origin: var(--radix-select-content-transform-origin);
    }

    .origin-\(--radix-tooltip-content-transform-origin\) {
        transform-origin: var(--radix-tooltip-content-transform-origin);
    }

    .-translate-x-0\.5 {
        --tw-translate-x: calc(var(--spacing) * -.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .-translate-x-1\/2 {
        --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .-translate-x-px {
        --tw-translate-x: -1px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .translate-x-\[-50\%\] {
        --tw-translate-x: -50%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .translate-x-px {
        --tw-translate-x: 1px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .-translate-y-1\/2 {
        --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .translate-y-0\.5 {
        --tw-translate-y: calc(var(--spacing) * .5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .translate-y-\[-50\%\] {
        --tw-translate-y: -50%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .translate-y-\[calc\(-50\%_-_2px\)\] {
        --tw-translate-y: calc(-50% - 2px);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .scale-105 {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .rotate-45 {
        rotate: 45deg;
    }

    .rotate-90 {
        rotate: 90deg;
    }

    .transform {
        transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
    }

    .animate-caret-blink {
        animation: 1.25s ease-out infinite caret-blink;
    }

    .animate-in {
        animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .animate-pulse {
        animation: var(--animate-pulse);
    }

    .cursor-default {
        cursor: default;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .touch-none {
        touch-action: none;
    }

    .resize-none {
        resize: none;
    }

    .scroll-my-1 {
        scroll-margin-block: calc(var(--spacing) * 1);
    }

    .scroll-py-1 {
        scroll-padding-block: calc(var(--spacing) * 1);
    }

    .list-none {
        list-style-type: none;
    }

    .auto-rows-min {
        grid-auto-rows: min-content;
    }

    .grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    .grid-cols-\[0_1fr\] {
        grid-template-columns: 0 1fr;
    }

    .grid-rows-\[auto_auto\] {
        grid-template-rows: auto auto;
    }

    .flex-col {
        flex-direction: column;
    }

    .flex-col-reverse {
        flex-direction: column-reverse;
    }

    .flex-row {
        flex-direction: row;
    }

    .flex-row-reverse {
        flex-direction: row-reverse;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .items-center {
        align-items: center;
    }

    .items-end {
        align-items: flex-end;
    }

    .items-start {
        align-items: flex-start;
    }

    .items-stretch {
        align-items: stretch;
    }

    .justify-between {
        justify-content: space-between;
    }

    .justify-center {
        justify-content: center;
    }

    .justify-end {
        justify-content: flex-end;
    }

    .justify-items-start {
        justify-items: start;
    }

    .gap-1 {
        gap: calc(var(--spacing) * 1);
    }

    .gap-1\.5 {
        gap: calc(var(--spacing) * 1.5);
    }

    .gap-2 {
        gap: calc(var(--spacing) * 2);
    }

    .gap-3 {
        gap: calc(var(--spacing) * 3);
    }

    .gap-4 {
        gap: calc(var(--spacing) * 4);
    }

    .gap-6 {
        gap: calc(var(--spacing) * 6);
    }

    .gap-8 {
        gap: calc(var(--spacing) * 8);
    }

    .gap-12 {
        gap: calc(var(--spacing) * 12);
    }

    .gap-16 {
        gap: calc(var(--spacing) * 16);
    }

    :where(.space-y-2 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-3 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-4 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-6 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-8 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-12 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-y-16 > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.space-x-1 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-2 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-3 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-4 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-6 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-8 > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.space-x-reverse > :not(:last-child)) {
        --tw-space-x-reverse: 1;
    }

    .gap-y-0\.5 {
        row-gap: calc(var(--spacing) * .5);
    }

    .self-start {
        align-self: flex-start;
    }

    .justify-self-end {
        justify-self: flex-end;
    }

    .overflow-auto {
        overflow: auto;
    }

    .overflow-hidden {
        overflow: hidden;
    }

    .overflow-x-auto {
        overflow-x: auto;
    }

    .overflow-x-hidden {
        overflow-x: hidden;
    }

    .overflow-y-auto {
        overflow-y: auto;
    }

    .rounded-2xl {
        border-radius: var(--radius-2xl);
    }

    .rounded-3xl {
        border-radius: var(--radius-3xl);
    }

    .rounded-\[2px\] {
        border-radius: 2px;
    }

    .rounded-\[4px\] {
        border-radius: 4px;
    }

    .rounded-\[inherit\] {
        border-radius: inherit;
    }

    .rounded-full {
        border-radius: 3.40282e38px;
    }

    .rounded-lg {
        border-radius: var(--radius);
    }

    .rounded-md {
        border-radius: calc(var(--radius)  - 2px);
    }

    .rounded-none {
        border-radius: 0;
    }

    .rounded-sm {
        border-radius: calc(var(--radius)  - 4px);
    }

    .rounded-xl {
        border-radius: calc(var(--radius)  + 4px);
    }

    .rounded-xs {
        border-radius: var(--radius-xs);
    }

    .rounded-tl-full {
        border-top-left-radius: 3.40282e38px;
    }

    .rounded-tl-sm {
        border-top-left-radius: calc(var(--radius)  - 4px);
    }

    .rounded-tr-3xl {
        border-top-right-radius: var(--radius-3xl);
    }

    .rounded-b-3xl {
        border-bottom-right-radius: var(--radius-3xl);
        border-bottom-left-radius: var(--radius-3xl);
    }

    .border {
        border-style: var(--tw-border-style);
        border-width: 1px;
    }

    .border-0 {
        border-style: var(--tw-border-style);
        border-width: 0;
    }

    .border-2 {
        border-style: var(--tw-border-style);
        border-width: 2px;
    }

    .border-4 {
        border-style: var(--tw-border-style);
        border-width: 4px;
    }

    .border-\[1\.5px\] {
        border-style: var(--tw-border-style);
        border-width: 1.5px;
    }

    .border-y {
        border-block-style: var(--tw-border-style);
        border-block-width: 1px;
    }

    .border-t {
        border-top-style: var(--tw-border-style);
        border-top-width: 1px;
    }

    .border-r {
        border-right-style: var(--tw-border-style);
        border-right-width: 1px;
    }

    .border-b {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
    }

    .border-l {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
    }

    .border-l-4 {
        border-left-style: var(--tw-border-style);
        border-left-width: 4px;
    }

    .border-dashed {
        --tw-border-style: dashed;
        border-style: dashed;
    }

    .border-\(--color-border\) {
        border-color: var(--color-border);
    }

    .border-border\/50 {
        border-color: var(--border);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-border\/50 {
            border-color: color-mix(in oklab, var(--border) 50%, transparent);
        }
    }

    .border-brand-orange\/20 {
        border-color: var(--brand-orange);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-brand-orange\/20 {
            border-color: color-mix(in oklab, var(--brand-orange) 20%, transparent);
        }
    }

    .border-brand-purple\/20 {
        border-color: var(--brand-purple);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-brand-purple\/20 {
            border-color: color-mix(in oklab, var(--brand-purple) 20%, transparent);
        }
    }

    .border-brand-red\/20 {
        border-color: var(--brand-red);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-brand-red\/20 {
            border-color: color-mix(in oklab, var(--brand-red) 20%, transparent);
        }
    }

    .border-brand-teal\/20 {
        border-color: var(--brand-teal);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-brand-teal\/20 {
            border-color: color-mix(in oklab, var(--brand-teal) 20%, transparent);
        }
    }

    .border-community-neutral-gray\/30 {
        border-color: var(--community-neutral-gray);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-community-neutral-gray\/30 {
            border-color: color-mix(in oklab, var(--community-neutral-gray) 30%, transparent);
        }
    }

    .border-gray-100 {
        border-color: var(--color-gray-100);
    }

    .border-gray-200 {
        border-color: var(--color-gray-200);
    }

    .border-input {
        border-color: var(--input);
    }

    .border-primary {
        border-color: var(--primary);
    }

    .border-sidebar-border {
        border-color: var(--sidebar-border);
    }

    .border-slate-200 {
        border-color: var(--color-slate-200);
    }

    .border-transparent {
        border-color: #0000;
    }

    .border-white {
        border-color: var(--color-white);
    }

    .border-white\/20 {
        border-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-white\/20 {
            border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
    }

    .border-white\/40 {
        border-color: #fff6;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .border-white\/40 {
            border-color: color-mix(in oklab, var(--color-white) 40%, transparent);
        }
    }

    .border-t-transparent {
        border-top-color: #0000;
    }

    .border-l-transparent {
        border-left-color: #0000;
    }

    .bg-\(--color-bg\) {
        background-color: var(--color-bg);
    }

    .bg-\[\#0077B5\] {
        background-color: #0077b5;
    }

    .bg-accent {
        background-color: var(--accent);
    }

    .bg-background {
        background-color: var(--background);
    }

    .bg-black\/50 {
        background-color: #00000080;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-black\/50 {
            background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
        }
    }

    .bg-border {
        background-color: var(--border);
    }

    .bg-brand-light-gray\/30 {
        background-color: var(--brand-light-gray);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-light-gray\/30 {
            background-color: color-mix(in oklab, var(--brand-light-gray) 30%, transparent);
        }
    }

    .bg-brand-light-gray\/50 {
        background-color: var(--brand-light-gray);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-light-gray\/50 {
            background-color: color-mix(in oklab, var(--brand-light-gray) 50%, transparent);
        }
    }

    .bg-brand-navy\/10 {
        background-color: var(--brand-navy);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-navy\/10 {
            background-color: color-mix(in oklab, var(--brand-navy) 10%, transparent);
        }
    }

    .bg-brand-navy\/90 {
        background-color: var(--brand-navy);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-navy\/90 {
            background-color: color-mix(in oklab, var(--brand-navy) 90%, transparent);
        }
    }

    .bg-brand-orange\/10 {
        background-color: var(--brand-orange);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-orange\/10 {
            background-color: color-mix(in oklab, var(--brand-orange) 10%, transparent);
        }
    }

    .bg-brand-orange\/90 {
        background-color: var(--brand-orange);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-orange\/90 {
            background-color: color-mix(in oklab, var(--brand-orange) 90%, transparent);
        }
    }

    .bg-brand-purple\/10 {
        background-color: var(--brand-purple);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-purple\/10 {
            background-color: color-mix(in oklab, var(--brand-purple) 10%, transparent);
        }
    }

    .bg-brand-red\/10 {
        background-color: var(--brand-red);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-red\/10 {
            background-color: color-mix(in oklab, var(--brand-red) 10%, transparent);
        }
    }

    .bg-brand-teal\/10 {
        background-color: var(--brand-teal);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-teal\/10 {
            background-color: color-mix(in oklab, var(--brand-teal) 10%, transparent);
        }
    }

    .bg-brand-teal\/30 {
        background-color: var(--brand-teal);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-brand-teal\/30 {
            background-color: color-mix(in oklab, var(--brand-teal) 30%, transparent);
        }
    }

    .bg-card {
        background-color: var(--card);
    }

    .bg-destructive {
        background-color: var(--destructive);
    }

    .bg-foreground {
        background-color: var(--foreground);
    }

    .bg-gray-100 {
        background-color: var(--color-gray-100);
    }

    .bg-input-background {
        background-color: var(--input-background);
    }

    .bg-muted {
        background-color: var(--muted);
    }

    .bg-muted\/50 {
        background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-muted\/50 {
            background-color: color-mix(in oklab, var(--muted) 50%, transparent);
        }
    }

    .bg-orange-50 {
        background-color: var(--color-orange-50);
    }

    .bg-popover {
        background-color: var(--popover);
    }

    .bg-primary {
        background-color: var(--primary);
    }

    .bg-primary\/20 {
        background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-primary\/20 {
            background-color: color-mix(in oklab, var(--primary) 20%, transparent);
        }
    }

    .bg-purple-50 {
        background-color: var(--color-purple-50);
    }

    .bg-red-50 {
        background-color: var(--color-red-50);
    }

    .bg-secondary {
        background-color: var(--secondary);
    }

    .bg-sidebar {
        background-color: var(--sidebar);
    }

    .bg-sidebar-border {
        background-color: var(--sidebar-border);
    }

    .bg-slate-50 {
        background-color: var(--color-slate-50);
    }

    .bg-slate-200 {
        background-color: var(--color-slate-200);
    }

    .bg-transparent {
        background-color: #0000;
    }

    .bg-white {
        background-color: var(--color-white);
    }

    .bg-white\/10 {
        background-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/10 {
            background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
    }

    .bg-white\/15 {
        background-color: #ffffff26;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/15 {
            background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
        }
    }

    .bg-white\/20 {
        background-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/20 {
            background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
    }

    .bg-white\/25 {
        background-color: #ffffff40;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/25 {
            background-color: color-mix(in oklab, var(--color-white) 25%, transparent);
        }
    }

    .bg-white\/30 {
        background-color: #ffffff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/30 {
            background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
        }
    }

    .bg-white\/40 {
        background-color: #fff6;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/40 {
            background-color: color-mix(in oklab, var(--color-white) 40%, transparent);
        }
    }

    .bg-white\/95 {
        background-color: #fffffff2;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .bg-white\/95 {
            background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
        }
    }

    .bg-gradient-to-bl {
        --tw-gradient-position: to bottom left in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
    }

    .bg-gradient-to-br {
        --tw-gradient-position: to bottom right in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
    }

    .bg-gradient-to-r {
        --tw-gradient-position: to right in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
    }

    .bg-gradient-to-t {
        --tw-gradient-position: to top in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
    }

    .bg-gradient-to-tl {
        --tw-gradient-position: to top left in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
    }

    .from-\[\#00B2A9\] {
        --tw-gradient-from: #00b2a9;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-brand-navy {
        --tw-gradient-from: var(--brand-navy);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-brand-orange {
        --tw-gradient-from: var(--brand-orange);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-brand-orange\/10 {
        --tw-gradient-from: var(--brand-orange);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-brand-orange\/10 {
            --tw-gradient-from: color-mix(in oklab, var(--brand-orange) 10%, transparent);
        }
    }

    .from-brand-orange\/30 {
        --tw-gradient-from: var(--brand-orange);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-brand-orange\/30 {
            --tw-gradient-from: color-mix(in oklab, var(--brand-orange) 30%, transparent);
        }
    }

    .from-brand-purple {
        --tw-gradient-from: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-brand-purple\/10 {
        --tw-gradient-from: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-brand-purple\/10 {
            --tw-gradient-from: color-mix(in oklab, var(--brand-purple) 10%, transparent);
        }
    }

    .from-brand-purple\/40 {
        --tw-gradient-from: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-brand-purple\/40 {
            --tw-gradient-from: color-mix(in oklab, var(--brand-purple) 40%, transparent);
        }
    }

    .from-brand-red {
        --tw-gradient-from: var(--brand-red);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-brand-red\/10 {
        --tw-gradient-from: var(--brand-red);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-brand-red\/10 {
            --tw-gradient-from: color-mix(in oklab, var(--brand-red) 10%, transparent);
        }
    }

    .from-community-navy {
        --tw-gradient-from: var(--community-navy);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-community-sky-blue\/10 {
        --tw-gradient-from: var(--community-sky-blue);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-community-sky-blue\/10 {
            --tw-gradient-from: color-mix(in oklab, var(--community-sky-blue) 10%, transparent);
        }
    }

    .from-transparent {
        --tw-gradient-from: transparent;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .from-white\/10 {
        --tw-gradient-from: #ffffff1a;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .from-white\/10 {
            --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
    }

    .via-\[\#802788\] {
        --tw-gradient-via: #802788;
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-brand-light-gray {
        --tw-gradient-via: var(--brand-light-gray);
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-brand-purple {
        --tw-gradient-via: var(--brand-purple);
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-brand-red {
        --tw-gradient-via: var(--brand-red);
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-community-purple {
        --tw-gradient-via: var(--community-purple);
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-current {
        --tw-gradient-via: currentcolor;
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-transparent {
        --tw-gradient-via: transparent;
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .via-white {
        --tw-gradient-via: var(--color-white);
        --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-via-stops);
    }

    .to-\[\#00B2A9\] {
        --tw-gradient-to: #00b2a9;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .to-brand-navy {
        --tw-gradient-to: var(--brand-navy);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .to-brand-orange\/5 {
        --tw-gradient-to: var(--brand-orange);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-orange\/5 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-orange) 5%, transparent);
        }
    }

    .to-brand-orange\/80 {
        --tw-gradient-to: var(--brand-orange);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-orange\/80 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-orange) 80%, transparent);
        }
    }

    .to-brand-purple {
        --tw-gradient-to: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .to-brand-purple\/5 {
        --tw-gradient-to: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-purple\/5 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-purple) 5%, transparent);
        }
    }

    .to-brand-purple\/80 {
        --tw-gradient-to: var(--brand-purple);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-purple\/80 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-purple) 80%, transparent);
        }
    }

    .to-brand-red\/5 {
        --tw-gradient-to: var(--brand-red);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-red\/5 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-red) 5%, transparent);
        }
    }

    .to-brand-red\/80 {
        --tw-gradient-to: var(--brand-red);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-red\/80 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-red) 80%, transparent);
        }
    }

    .to-brand-red\/90 {
        --tw-gradient-to: var(--brand-red);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-brand-red\/90 {
            --tw-gradient-to: color-mix(in oklab, var(--brand-red) 90%, transparent);
        }
    }

    .to-community-neutral-gray\/20 {
        --tw-gradient-to: var(--community-neutral-gray);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-community-neutral-gray\/20 {
            --tw-gradient-to: color-mix(in oklab, var(--community-neutral-gray) 20%, transparent);
        }
    }

    .to-community-teal {
        --tw-gradient-to: var(--community-teal);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .to-transparent {
        --tw-gradient-to: transparent;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .to-white\/5 {
        --tw-gradient-to: #ffffff0d;
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
        .to-white\/5 {
            --tw-gradient-to: color-mix(in oklab, var(--color-white) 5%, transparent);
        }
    }

    .bg-cover {
        background-size: cover;
    }

    .bg-clip-text {
        background-clip: text;
    }

    .bg-center {
        background-position: center;
    }

    .bg-no-repeat {
        background-repeat: no-repeat;
    }

    .fill-current {
        fill: currentColor;
    }

    .fill-primary {
        fill: var(--primary);
    }

    .object-contain {
        object-fit: contain;
    }

    .object-cover {
        object-fit: cover;
    }

    .p-0 {
        padding: calc(var(--spacing) * 0);
    }

    .p-1 {
        padding: calc(var(--spacing) * 1);
    }

    .p-1\.5 {
        padding: calc(var(--spacing) * 1.5);
    }

    .p-2 {
        padding: calc(var(--spacing) * 2);
    }

    .p-3 {
        padding: calc(var(--spacing) * 3);
    }

    .p-4 {
        padding: calc(var(--spacing) * 4);
    }

    .p-6 {
        padding: calc(var(--spacing) * 6);
    }

    .p-8 {
        padding: calc(var(--spacing) * 8);
    }

    .p-\[3px\] {
        padding: 3px;
    }

    .p-px {
        padding: 1px;
    }

    .px-1 {
        padding-inline: calc(var(--spacing) * 1);
    }

    .px-1\.5 {
        padding-inline: calc(var(--spacing) * 1.5);
    }

    .px-2 {
        padding-inline: calc(var(--spacing) * 2);
    }

    .px-2\.5 {
        padding-inline: calc(var(--spacing) * 2.5);
    }

    .px-3 {
        padding-inline: calc(var(--spacing) * 3);
    }

    .px-4 {
        padding-inline: calc(var(--spacing) * 4);
    }

    .px-6 {
        padding-inline: calc(var(--spacing) * 6);
    }

    .px-8 {
        padding-inline: calc(var(--spacing) * 8);
    }

    .py-0\.5 {
        padding-block: calc(var(--spacing) * .5);
    }

    .py-1 {
        padding-block: calc(var(--spacing) * 1);
    }

    .py-1\.5 {
        padding-block: calc(var(--spacing) * 1.5);
    }

    .py-2 {
        padding-block: calc(var(--spacing) * 2);
    }

    .py-3 {
        padding-block: calc(var(--spacing) * 3);
    }

    .py-4 {
        padding-block: calc(var(--spacing) * 4);
    }

    .py-6 {
        padding-block: calc(var(--spacing) * 6);
    }

    .py-8 {
        padding-block: calc(var(--spacing) * 8);
    }

    .py-12 {
        padding-block: calc(var(--spacing) * 12);
    }

    .py-16 {
        padding-block: calc(var(--spacing) * 16);
    }

    .py-20 {
        padding-block: calc(var(--spacing) * 20);
    }

    .py-24 {
        padding-block: calc(var(--spacing) * 24);
    }

    .pt-0 {
        padding-top: calc(var(--spacing) * 0);
    }

    .pt-1 {
        padding-top: calc(var(--spacing) * 1);
    }

    .pt-3 {
        padding-top: calc(var(--spacing) * 3);
    }

    .pt-4 {
        padding-top: calc(var(--spacing) * 4);
    }

    .pt-6 {
        padding-top: calc(var(--spacing) * 6);
    }

    .pt-8 {
        padding-top: calc(var(--spacing) * 8);
    }

    .pt-20 {
        padding-top: calc(var(--spacing) * 20);
    }

    .pr-2 {
        padding-right: calc(var(--spacing) * 2);
    }

    .pr-2\.5 {
        padding-right: calc(var(--spacing) * 2.5);
    }

    .pr-8 {
        padding-right: calc(var(--spacing) * 8);
    }

    .pb-0 {
        padding-bottom: calc(var(--spacing) * 0);
    }

    .pb-1 {
        padding-bottom: calc(var(--spacing) * 1);
    }

    .pb-2 {
        padding-bottom: calc(var(--spacing) * 2);
    }

    .pb-3 {
        padding-bottom: calc(var(--spacing) * 3);
    }

    .pb-4 {
        padding-bottom: calc(var(--spacing) * 4);
    }

    .pb-6 {
        padding-bottom: calc(var(--spacing) * 6);
    }

    .pb-8 {
        padding-bottom: calc(var(--spacing) * 8);
    }

    .pb-12 {
        padding-bottom: calc(var(--spacing) * 12);
    }

    .pb-20 {
        padding-bottom: calc(var(--spacing) * 20);
    }

    .pl-2 {
        padding-left: calc(var(--spacing) * 2);
    }

    .pl-4 {
        padding-left: calc(var(--spacing) * 4);
    }

    .pl-8 {
        padding-left: calc(var(--spacing) * 8);
    }

    .text-center {
        text-align: center;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .align-middle {
        vertical-align: middle;
    }

    .font-mono {
        font-family: var(--font-mono);
    }

    .text-2xl {
        font-size: var(--text-2xl);
        line-height: var(--tw-leading, var(--text-2xl--line-height));
    }

    .text-3xl {
        font-size: var(--text-3xl);
        line-height: var(--tw-leading, var(--text-3xl--line-height));
    }

    .text-4xl {
        font-size: var(--text-4xl);
        line-height: var(--tw-leading, var(--text-4xl--line-height));
    }

    .text-5xl {
        font-size: var(--text-5xl);
        line-height: var(--tw-leading, var(--text-5xl--line-height));
    }

    .text-6xl {
        font-size: var(--text-6xl);
        line-height: var(--tw-leading, var(--text-6xl--line-height));
    }

    .text-base {
        font-size: var(--text-base);
        line-height: var(--tw-leading, var(--text-base--line-height));
    }

    .text-lg {
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
    }

    .text-sm {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
    }

    .text-xl {
        font-size: var(--text-xl);
        line-height: var(--tw-leading, var(--text-xl--line-height));
    }

    .text-xs {
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
    }

    .text-\[0\.8rem\] {
        font-size: .8rem;
    }

    .leading-none {
        --tw-leading: 1;
        line-height: 1;
    }

    .leading-relaxed {
        --tw-leading: var(--leading-relaxed);
        line-height: var(--leading-relaxed);
    }

    .leading-tight {
        --tw-leading: var(--leading-tight);
        line-height: var(--leading-tight);
    }

    .font-bold {
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
    }

    .font-medium {
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
    }

    .font-normal {
        --tw-font-weight: var(--font-weight-normal);
        font-weight: var(--font-weight-normal);
    }

    .font-semibold {
        --tw-font-weight: var(--font-weight-semibold);
        font-weight: var(--font-weight-semibold);
    }

    .tracking-tight {
        --tw-tracking: var(--tracking-tight);
        letter-spacing: var(--tracking-tight);
    }

    .tracking-wider {
        --tw-tracking: var(--tracking-wider);
        letter-spacing: var(--tracking-wider);
    }

    .tracking-widest {
        --tw-tracking: var(--tracking-widest);
        letter-spacing: var(--tracking-widest);
    }

    .text-balance {
        text-wrap: balance;
    }

    .break-words {
        overflow-wrap: break-word;
    }

    .whitespace-nowrap {
        white-space: nowrap;
    }

    .text-accent-foreground {
        color: var(--accent-foreground);
    }

    .text-brand-teal\/60 {
        color: var(--brand-teal);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-brand-teal\/60 {
            color: color-mix(in oklab, var(--brand-teal) 60%, transparent);
        }
    }

    .text-card-foreground {
        color: var(--card-foreground);
    }

    .text-community-dark-blue\/60 {
        color: var(--community-dark-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-community-dark-blue\/60 {
            color: color-mix(in oklab, var(--community-dark-blue) 60%, transparent);
        }
    }

    .text-current {
        color: currentColor;
    }

    .text-destructive {
        color: var(--destructive);
    }

    .text-foreground {
        color: var(--foreground);
    }

    .text-gray-600 {
        color: var(--color-gray-600);
    }

    .text-gray-700 {
        color: var(--color-gray-700);
    }

    .text-gray-800 {
        color: var(--color-gray-800);
    }

    .text-muted-foreground {
        color: var(--muted-foreground);
    }

    .text-popover-foreground {
        color: var(--popover-foreground);
    }

    .text-primary {
        color: var(--primary);
    }

    .text-primary-foreground {
        color: var(--primary-foreground);
    }

    .text-secondary-foreground {
        color: var(--secondary-foreground);
    }

    .text-sidebar-foreground {
        color: var(--sidebar-foreground);
    }

    .text-sidebar-foreground\/70 {
        color: var(--sidebar-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-sidebar-foreground\/70 {
            color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
        }
    }

    .text-transparent {
        color: #0000;
    }

    .text-white {
        color: var(--color-white);
    }

    .text-white\/70 {
        color: #ffffffb3;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-white\/70 {
            color: color-mix(in oklab, var(--color-white) 70%, transparent);
        }
    }

    .text-white\/80 {
        color: #fffc;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-white\/80 {
            color: color-mix(in oklab, var(--color-white) 80%, transparent);
        }
    }

    .text-white\/90 {
        color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
        .text-white\/90 {
            color: color-mix(in oklab, var(--color-white) 90%, transparent);
        }
    }

    .uppercase {
        text-transform: uppercase;
    }

    .italic {
        font-style: italic;
    }

    .tabular-nums {
        --tw-numeric-spacing: tabular-nums;
        font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
    }

    .underline-offset-4 {
        text-underline-offset: 4px;
    }

    .opacity-0 {
        opacity: 0;
    }

    .opacity-1 {
        opacity: .01;
    }

    .opacity-5 {
        opacity: .05;
    }

    .opacity-15 {
        opacity: .15;
    }

    .opacity-18 {
        opacity: .18;
    }

    .opacity-20 {
        opacity: .2;
    }

    .opacity-30 {
        opacity: .3;
    }

    .opacity-50 {
        opacity: .5;
    }

    .opacity-60 {
        opacity: .6;
    }

    .opacity-70 {
        opacity: .7;
    }

    .opacity-80 {
        opacity: .8;
    }

    .opacity-\[0\.03\] {
        opacity: .03;
    }

    .shadow {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-lg {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-md {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-none {
        --tw-shadow: 0 0 #0000;
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-sm {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-xl {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .shadow-xs {
        --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .ring-0 {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .ring-ring\/50 {
        --tw-ring-color: var(--ring);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .ring-ring\/50 {
            --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
        }
    }

    .ring-sidebar-ring {
        --tw-ring-color: var(--sidebar-ring);
    }

    .ring-offset-background {
        --tw-ring-offset-color: var(--background);
    }

    .outline-hidden {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .outline-hidden {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .outline {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
    }

    .filter {
        filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }

    .backdrop-blur {
        --tw-backdrop-blur: blur(8px);
        -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
        backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    }

    .backdrop-blur-sm {
        --tw-backdrop-blur: blur(var(--blur-sm));
        -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
        backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    }

    .backdrop-filter {
        -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
        backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    }

    .transition {
        transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-\[color\,box-shadow\] {
        transition-property: color, box-shadow;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-\[left\,right\,width\] {
        transition-property: left, right, width;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-\[margin\,opacity\] {
        transition-property: margin, opacity;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-\[width\,height\,padding\] {
        transition-property: width, height, padding;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-\[width\] {
        transition-property: width;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-all {
        transition-property: all;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-colors {
        transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-opacity {
        transition-property: opacity;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-shadow {
        transition-property: box-shadow;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-transform {
        transition-property: transform, translate, scale, rotate;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
    }

    .transition-none {
        transition-property: none;
    }

    .delay-300 {
        transition-delay: .3s;
    }

    .delay-500 {
        transition-delay: .5s;
    }

    .delay-700 {
        transition-delay: .7s;
    }

    .delay-1000 {
        transition-delay: 1s;
    }

    .duration-200 {
        --tw-duration: .2s;
        transition-duration: .2s;
    }

    .duration-300 {
        --tw-duration: .3s;
        transition-duration: .3s;
    }

    .duration-500 {
        --tw-duration: .5s;
        transition-duration: .5s;
    }

    .duration-1000 {
        --tw-duration: 1s;
        transition-duration: 1s;
    }

    .ease-in-out {
        --tw-ease: var(--ease-in-out);
        transition-timing-function: var(--ease-in-out);
    }

    .ease-linear {
        --tw-ease: linear;
        transition-timing-function: linear;
    }

    .delay-300 {
        animation-delay: .3s;
    }

    .delay-500 {
        animation-delay: .5s;
    }

    .delay-700 {
        animation-delay: .7s;
    }

    .delay-1000 {
        animation-delay: 1s;
    }

    .outline-none {
        --tw-outline-style: none;
        outline-style: none;
    }

    .select-none {
        -webkit-user-select: none;
        user-select: none;
    }

    .fade-in-0 {
        --tw-enter-opacity: 0;
    }

    .zoom-in-95 {
        --tw-enter-scale: .95;
    }

    .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
        opacity: 1;
    }

    @media (hover: hover) {
        .group-hover\:translate-x-1:is(:where(.group):hover *) {
            --tw-translate-x: calc(var(--spacing) * 1);
            translate: var(--tw-translate-x) var(--tw-translate-y);
        }
    }

    @media (hover: hover) {
        .group-hover\:-translate-y-2:is(:where(.group):hover *) {
            --tw-translate-y: calc(var(--spacing) * -2);
            translate: var(--tw-translate-x) var(--tw-translate-y);
        }
    }

    @media (hover: hover) {
        .group-hover\:scale-105:is(:where(.group):hover *) {
            --tw-scale-x: 105%;
            --tw-scale-y: 105%;
            --tw-scale-z: 105%;
            scale: var(--tw-scale-x) var(--tw-scale-y);
        }
    }

    @media (hover: hover) {
        .group-hover\:scale-110:is(:where(.group):hover *) {
            --tw-scale-x: 110%;
            --tw-scale-y: 110%;
            --tw-scale-z: 110%;
            scale: var(--tw-scale-x) var(--tw-scale-y);
        }
    }

    @media (hover: hover) {
        .group-hover\:border-white:is(:where(.group):hover *) {
            border-color: var(--color-white);
        }
    }

    @media (hover: hover) {
        .group-hover\:bg-brand-red:is(:where(.group):hover *) {
            background-color: var(--brand-red);
        }
    }

    @media (hover: hover) {
        .group-hover\:bg-brand-red\/20:is(:where(.group):hover *) {
            background-color: var(--brand-red);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .group-hover\:bg-brand-red\/20:is(:where(.group):hover *) {
                background-color: color-mix(in oklab, var(--brand-red) 20%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .group-hover\:bg-white:is(:where(.group):hover *) {
            background-color: var(--color-white);
        }
    }

    @media (hover: hover) {
        .group-hover\:bg-white\/20:is(:where(.group):hover *) {
            background-color: #fff3;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .group-hover\:bg-white\/20:is(:where(.group):hover *) {
                background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .group-hover\:from-white\/20:is(:where(.group):hover *) {
            --tw-gradient-from: #fff3;
            --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
        }

        @supports (color: color-mix(in lab, red, red)) {
            .group-hover\:from-white\/20:is(:where(.group):hover *) {
                --tw-gradient-from: color-mix(in oklab, var(--color-white) 20%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .group-hover\:text-brand-navy:is(:where(.group):hover *) {
            color: var(--brand-navy);
        }
    }

    @media (hover: hover) {
        .group-hover\:text-brand-red:is(:where(.group):hover *) {
            color: var(--brand-red);
        }
    }

    @media (hover: hover) {
        .group-hover\:text-white:is(:where(.group):hover *) {
            color: var(--color-white);
        }
    }

    @media (hover: hover) {
        .group-hover\:text-white\/80:is(:where(.group):hover *) {
            color: #fffc;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .group-hover\:text-white\/80:is(:where(.group):hover *) {
                color: color-mix(in oklab, var(--color-white) 80%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .group-hover\:text-white\/90:is(:where(.group):hover *) {
            color: #ffffffe6;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .group-hover\:text-white\/90:is(:where(.group):hover *) {
                color: color-mix(in oklab, var(--color-white) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .group-hover\:opacity-100:is(:where(.group):hover *) {
            opacity: 1;
        }
    }

    @media (hover: hover) {
        .group-hover\:shadow-lg:is(:where(.group):hover *) {
            --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .group-hover\:shadow-xl:is(:where(.group):hover *) {
            --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .group-hover\:backdrop-blur-sm:is(:where(.group):hover *) {
            --tw-backdrop-blur: blur(var(--blur-sm));
            -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
            backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
        }
    }

    @media (hover: hover) {
        .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
            opacity: 1;
        }
    }

    .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
        padding-right: calc(var(--spacing) * 8);
    }

    .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
        margin-top: calc(var(--spacing) * -8);
    }

    .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
        display: none;
    }

    .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
        width: calc(var(--spacing) * 8) !important;
        height: calc(var(--spacing) * 8) !important;
    }

    .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
        width: var(--sidebar-width-icon);
    }

    .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
        width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
    }

    .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
        width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
    }

    .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
        overflow: hidden;
    }

    .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
        padding: calc(var(--spacing) * 0) !important;
    }

    .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
        padding: calc(var(--spacing) * 2) !important;
    }

    .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
        opacity: 0;
    }

    .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
        right: calc(var(--sidebar-width) * -1);
    }

    .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
        left: calc(var(--sidebar-width) * -1);
    }

    .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
        width: calc(var(--spacing) * 0);
    }

    .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
        pointer-events: none;
    }

    .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
        opacity: .5;
    }

    .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
        right: calc(var(--spacing) * -4);
    }

    .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
        border-right-style: var(--tw-border-style);
        border-right-width: 1px;
    }

    .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
        left: calc(var(--spacing) * 0);
    }

    .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
        rotate: 180deg;
    }

    .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
    }

    .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
        rotate: 180deg;
    }

    .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
        border-radius: var(--radius);
    }

    .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
        border-style: var(--tw-border-style);
        border-width: 1px;
    }

    .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
        border-color: var(--sidebar-border);
    }

    .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
        display: block;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        top: 100%;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        margin-top: calc(var(--spacing) * 1.5);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        overflow: hidden;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        border-radius: calc(var(--radius)  - 2px);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        border-style: var(--tw-border-style);
        border-width: 1px;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        background-color: var(--popover);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        color: var(--popover-foreground);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
        --tw-duration: .2s;
        transition-duration: .2s;
    }

    @media (hover: hover) {
        .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
            color: var(--sidebar-accent-foreground);
        }
    }

    .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
        cursor: not-allowed;
    }

    .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
        opacity: .5;
    }

    .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
        color: var(--sidebar-accent-foreground);
    }

    .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
        top: calc(var(--spacing) * 1.5);
    }

    .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
        top: calc(var(--spacing) * 2.5);
    }

    .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
        top: calc(var(--spacing) * 1);
    }

    .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
        background-color: var(--primary);
    }

    .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
        color: var(--primary-foreground);
    }

    .file\:inline-flex::file-selector-button {
        display: inline-flex;
    }

    .file\:h-7::file-selector-button {
        height: calc(var(--spacing) * 7);
    }

    .file\:border-0::file-selector-button {
        border-style: var(--tw-border-style);
        border-width: 0;
    }

    .file\:bg-transparent::file-selector-button {
        background-color: #0000;
    }

    .file\:text-sm::file-selector-button {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
    }

    .file\:font-medium::file-selector-button {
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
    }

    .file\:text-foreground::file-selector-button {
        color: var(--foreground);
    }

    .placeholder\:text-muted-foreground::placeholder {
        color: var(--muted-foreground);
    }

    .after\:absolute:after {
        content: var(--tw-content);
        position: absolute;
    }

    .after\:-inset-2:after {
        content: var(--tw-content);
        inset: calc(var(--spacing) * -2);
    }

    .after\:inset-y-0:after {
        content: var(--tw-content);
        inset-block: calc(var(--spacing) * 0);
    }

    .after\:right-0:after {
        content: var(--tw-content);
        right: calc(var(--spacing) * 0);
    }

    .after\:bottom-\[-8px\]:after {
        content: var(--tw-content);
        bottom: -8px;
    }

    .after\:left-0:after {
        content: var(--tw-content);
        left: calc(var(--spacing) * 0);
    }

    .after\:left-1\/2:after {
        content: var(--tw-content);
        left: 50%;
    }

    .after\:h-0\.5:after {
        content: var(--tw-content);
        height: calc(var(--spacing) * .5);
    }

    .after\:w-1:after {
        content: var(--tw-content);
        width: calc(var(--spacing) * 1);
    }

    .after\:w-\[2px\]:after {
        content: var(--tw-content);
        width: 2px;
    }

    .after\:-translate-x-1\/2:after {
        content: var(--tw-content);
        --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .after\:rounded-full:after {
        content: var(--tw-content);
        border-radius: 3.40282e38px;
    }

    .after\:bg-brand-red:after {
        content: var(--tw-content);
        background-color: var(--brand-red);
    }

    .after\:content-\[\"\"\]:after {
        content: var(--tw-content);
        --tw-content: "";
        content: var(--tw-content);
    }

    .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
        content: var(--tw-content);
        left: 100%;
    }

    .first\:rounded-l-md:first-child {
        border-top-left-radius: calc(var(--radius)  - 2px);
        border-bottom-left-radius: calc(var(--radius)  - 2px);
    }

    .first\:border-l:first-child {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
    }

    .last\:mb-0:last-child {
        margin-bottom: calc(var(--spacing) * 0);
    }

    .last\:rounded-r-md:last-child {
        border-top-right-radius: calc(var(--radius)  - 2px);
        border-bottom-right-radius: calc(var(--radius)  - 2px);
    }

    .last\:border-b-0:last-child {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 0;
    }

    .focus-within\:relative:focus-within {
        position: relative;
    }

    .focus-within\:z-20:focus-within {
        z-index: 20;
    }

    @media (hover: hover) {
        .hover\:-translate-y-1:hover {
            --tw-translate-y: calc(var(--spacing) * -1);
            translate: var(--tw-translate-x) var(--tw-translate-y);
        }
    }

    @media (hover: hover) {
        .hover\:scale-105:hover {
            --tw-scale-x: 105%;
            --tw-scale-y: 105%;
            --tw-scale-z: 105%;
            scale: var(--tw-scale-x) var(--tw-scale-y);
        }
    }

    @media (hover: hover) {
        .hover\:scale-\[1\.02\]:hover {
            scale: 1.02;
        }
    }

    @media (hover: hover) {
        .hover\:border-brand-navy:hover {
            border-color: var(--brand-navy);
        }
    }

    @media (hover: hover) {
        .hover\:border-brand-orange:hover {
            border-color: var(--brand-orange);
        }
    }

    @media (hover: hover) {
        .hover\:border-brand-purple:hover {
            border-color: var(--brand-purple);
        }
    }

    @media (hover: hover) {
        .hover\:border-brand-red:hover {
            border-color: var(--brand-red);
        }
    }

    @media (hover: hover) {
        .hover\:border-brand-teal\/40:hover {
            border-color: var(--brand-teal);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:border-brand-teal\/40:hover {
                border-color: color-mix(in oklab, var(--brand-teal) 40%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:border-community-teal:hover {
            border-color: var(--community-teal);
        }
    }

    @media (hover: hover) {
        .hover\:border-community-teal\/30:hover {
            border-color: var(--community-teal);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:border-community-teal\/30:hover {
                border-color: color-mix(in oklab, var(--community-teal) 30%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:border-current:hover {
            border-color: currentColor;
        }
    }

    @media (hover: hover) {
        .hover\:bg-\[\#004182\]:hover {
            background-color: #004182;
        }
    }

    @media (hover: hover) {
        .hover\:bg-accent:hover {
            background-color: var(--accent);
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-light-gray:hover {
            background-color: var(--brand-light-gray);
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-orange\/5:hover {
            background-color: var(--brand-orange);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-brand-orange\/5:hover {
                background-color: color-mix(in oklab, var(--brand-orange) 5%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-orange\/80:hover {
            background-color: var(--brand-orange);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-brand-orange\/80:hover {
                background-color: color-mix(in oklab, var(--brand-orange) 80%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-purple:hover {
            background-color: var(--brand-purple);
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-purple\/5:hover {
            background-color: var(--brand-purple);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-brand-purple\/5:hover {
                background-color: color-mix(in oklab, var(--brand-purple) 5%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-red:hover {
            background-color: var(--brand-red);
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-red\/5:hover {
            background-color: var(--brand-red);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-brand-red\/5:hover {
                background-color: color-mix(in oklab, var(--brand-red) 5%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-brand-red\/90:hover {
            background-color: var(--brand-red);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-brand-red\/90:hover {
                background-color: color-mix(in oklab, var(--brand-red) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-destructive\/90:hover {
            background-color: var(--destructive);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-destructive\/90:hover {
                background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-muted:hover {
            background-color: var(--muted);
        }
    }

    @media (hover: hover) {
        .hover\:bg-muted\/50:hover {
            background-color: var(--muted);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-muted\/50:hover {
                background-color: color-mix(in oklab, var(--muted) 50%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-primary:hover {
            background-color: var(--primary);
        }
    }

    @media (hover: hover) {
        .hover\:bg-primary\/90:hover {
            background-color: var(--primary);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-primary\/90:hover {
                background-color: color-mix(in oklab, var(--primary) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-secondary\/80:hover {
            background-color: var(--secondary);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-secondary\/80:hover {
                background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-sidebar-accent:hover {
            background-color: var(--sidebar-accent);
        }
    }

    @media (hover: hover) {
        .hover\:bg-slate-50:hover {
            background-color: var(--color-slate-50);
        }
    }

    @media (hover: hover) {
        .hover\:bg-white:hover {
            background-color: var(--color-white);
        }
    }

    @media (hover: hover) {
        .hover\:bg-white\/10:hover {
            background-color: #ffffff1a;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-white\/10:hover {
                background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:bg-white\/90:hover {
            background-color: #ffffffe6;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .hover\:bg-white\/90:hover {
                background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .hover\:text-accent-foreground:hover {
            color: var(--accent-foreground);
        }
    }

    @media (hover: hover) {
        .hover\:text-brand-navy:hover {
            color: var(--brand-navy);
        }
    }

    @media (hover: hover) {
        .hover\:text-brand-orange:hover {
            color: var(--brand-orange);
        }
    }

    @media (hover: hover) {
        .hover\:text-brand-purple:hover {
            color: var(--brand-purple);
        }
    }

    @media (hover: hover) {
        .hover\:text-brand-red:hover {
            color: var(--brand-red);
        }
    }

    @media (hover: hover) {
        .hover\:text-community-teal:hover {
            color: var(--community-teal);
        }
    }

    @media (hover: hover) {
        .hover\:text-foreground:hover {
            color: var(--foreground);
        }
    }

    @media (hover: hover) {
        .hover\:text-muted-foreground:hover {
            color: var(--muted-foreground);
        }
    }

    @media (hover: hover) {
        .hover\:text-primary-foreground:hover {
            color: var(--primary-foreground);
        }
    }

    @media (hover: hover) {
        .hover\:text-sidebar-accent-foreground:hover {
            color: var(--sidebar-accent-foreground);
        }
    }

    @media (hover: hover) {
        .hover\:text-white:hover {
            color: var(--color-white);
        }
    }

    @media (hover: hover) {
        .hover\:no-underline:hover {
            text-decoration-line: none;
        }
    }

    @media (hover: hover) {
        .hover\:underline:hover {
            text-decoration-line: underline;
        }
    }

    @media (hover: hover) {
        .hover\:opacity-100:hover {
            opacity: 1;
        }
    }

    @media (hover: hover) {
        .hover\:shadow-2xl:hover {
            --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
            --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:shadow-lg:hover {
            --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:shadow-md:hover {
            --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:shadow-xl:hover {
            --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:ring-4:hover {
            --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (hover: hover) {
        .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
            background-color: var(--sidebar);
        }
    }

    @media (hover: hover) {
        .hover\:after\:bg-sidebar-border:hover:after {
            content: var(--tw-content);
            background-color: var(--sidebar-border);
        }
    }

    .focus\:z-10:focus {
        z-index: 10;
    }

    .focus\:border-brand-red:focus {
        border-color: var(--brand-red);
    }

    .focus\:border-community-teal:focus {
        border-color: var(--community-teal);
    }

    .focus\:bg-accent:focus {
        background-color: var(--accent);
    }

    .focus\:bg-primary:focus {
        background-color: var(--primary);
    }

    .focus\:text-accent-foreground:focus {
        color: var(--accent-foreground);
    }

    .focus\:text-primary-foreground:focus {
        color: var(--primary-foreground);
    }

    .focus\:ring-2:focus {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .focus\:ring-brand-red:focus {
        --tw-ring-color: var(--brand-red);
    }

    .focus\:ring-community-teal:focus {
        --tw-ring-color: var(--community-teal);
    }

    .focus\:ring-ring:focus {
        --tw-ring-color: var(--ring);
    }

    .focus\:ring-offset-2:focus {
        --tw-ring-offset-width: 2px;
        --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }

    .focus\:outline-hidden:focus {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .focus\:outline-hidden:focus {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .focus-visible\:z-10:focus-visible {
        z-index: 10;
    }

    .focus-visible\:border-ring:focus-visible {
        border-color: var(--ring);
    }

    .focus-visible\:ring-1:focus-visible {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .focus-visible\:ring-2:focus-visible {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .focus-visible\:ring-4:focus-visible {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .focus-visible\:ring-\[3px\]:focus-visible {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .focus-visible\:ring-destructive\/20:focus-visible {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .focus-visible\:ring-destructive\/20:focus-visible {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
    }

    .focus-visible\:ring-ring:focus-visible {
        --tw-ring-color: var(--ring);
    }

    .focus-visible\:ring-ring\/50:focus-visible {
        --tw-ring-color: var(--ring);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .focus-visible\:ring-ring\/50:focus-visible {
            --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
        }
    }

    .focus-visible\:ring-offset-1:focus-visible {
        --tw-ring-offset-width: 1px;
        --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }

    .focus-visible\:outline-hidden:focus-visible {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .focus-visible\:outline-hidden:focus-visible {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .focus-visible\:outline-1:focus-visible {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
    }

    .focus-visible\:outline-ring:focus-visible {
        outline-color: var(--ring);
    }

    .active\:bg-sidebar-accent:active {
        background-color: var(--sidebar-accent);
    }

    .active\:text-sidebar-accent-foreground:active {
        color: var(--sidebar-accent-foreground);
    }

    .disabled\:pointer-events-none:disabled {
        pointer-events: none;
    }

    .disabled\:cursor-not-allowed:disabled {
        cursor: not-allowed;
    }

    .disabled\:opacity-50:disabled {
        opacity: .5;
    }

    :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
        cursor: w-resize;
    }

    :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
        cursor: e-resize;
    }

    .has-disabled\:opacity-50:has(:disabled) {
        opacity: .5;
    }

    .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
        grid-template-columns: 1fr auto;
    }

    .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
        background-color: var(--sidebar);
    }

    .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has( > svg) {
        grid-template-columns: calc(var(--spacing) * 4) 1fr;
    }

    .has-\[\>svg\]\:gap-x-3:has( > svg) {
        column-gap: calc(var(--spacing) * 3);
    }

    .has-\[\>svg\]\:px-2\.5:has( > svg) {
        padding-inline: calc(var(--spacing) * 2.5);
    }

    .has-\[\>svg\]\:px-3:has( > svg) {
        padding-inline: calc(var(--spacing) * 3);
    }

    .has-\[\>svg\]\:px-4:has( > svg) {
        padding-inline: calc(var(--spacing) * 4);
    }

    .aria-disabled\:pointer-events-none[aria-disabled="true"] {
        pointer-events: none;
    }

    .aria-disabled\:opacity-50[aria-disabled="true"] {
        opacity: .5;
    }

    .aria-invalid\:border-destructive[aria-invalid="true"] {
        border-color: var(--destructive);
    }

    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
    }

    .aria-selected\:bg-accent[aria-selected="true"] {
        background-color: var(--accent);
    }

    .aria-selected\:bg-primary[aria-selected="true"] {
        background-color: var(--primary);
    }

    .aria-selected\:text-accent-foreground[aria-selected="true"] {
        color: var(--accent-foreground);
    }

    .aria-selected\:text-muted-foreground[aria-selected="true"] {
        color: var(--muted-foreground);
    }

    .aria-selected\:text-primary-foreground[aria-selected="true"] {
        color: var(--primary-foreground);
    }

    .aria-selected\:opacity-100[aria-selected="true"] {
        opacity: 1;
    }

    .data-\[active\=true\]\:z-10[data-active="true"] {
        z-index: 10;
    }

    .data-\[active\=true\]\:border-ring[data-active="true"] {
        border-color: var(--ring);
    }

    .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
        background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
            background-color: color-mix(in oklab, var(--accent) 50%, transparent);
        }
    }

    .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
        background-color: var(--sidebar-accent);
    }

    .data-\[active\=true\]\:font-medium[data-active="true"] {
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
    }

    .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
        color: var(--accent-foreground);
    }

    .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
        color: var(--sidebar-accent-foreground);
    }

    .data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
        --tw-ring-color: var(--ring);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
            --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
        }
    }

    @media (hover: hover) {
        .data-\[active\=true\]\:hover\:bg-accent[data-active="true"]:hover {
            background-color: var(--accent);
        }
    }

    .data-\[active\=true\]\:focus\:bg-accent[data-active="true"]:focus {
        background-color: var(--accent);
    }

    .data-\[active\=true\]\:aria-invalid\:border-destructive[data-active="true"][aria-invalid="true"] {
        border-color: var(--destructive);
    }

    .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
    }

    .data-\[disabled\]\:pointer-events-none[data-disabled] {
        pointer-events: none;
    }

    .data-\[disabled\]\:opacity-50[data-disabled] {
        opacity: .5;
    }

    .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
        pointer-events: none;
    }

    .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
        opacity: .5;
    }

    .data-\[error\=true\]\:text-destructive[data-error="true"] {
        color: var(--destructive);
    }

    .data-\[inset\]\:pl-8[data-inset] {
        padding-left: calc(var(--spacing) * 8);
    }

    .data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
        --tw-enter-translate-x: calc(52 * var(--spacing));
    }

    .data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
        --tw-enter-translate-x: calc(52 * var(--spacing) * -1);
    }

    .data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
        --tw-exit-translate-x: calc(52 * var(--spacing));
    }

    .data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
        --tw-exit-translate-x: calc(52 * var(--spacing) * -1);
    }

    .data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
        animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
        --tw-enter-opacity: 0;
    }

    .data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
        animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
        --tw-exit-opacity: 0;
    }

    .data-\[orientation\=horizontal\]\:h-4[data-orientation="horizontal"] {
        height: calc(var(--spacing) * 4);
    }

    .data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
        height: 100%;
    }

    .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
        height: 1px;
    }

    .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
        width: 100%;
    }

    .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
        height: 100%;
    }

    .data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
        min-height: calc(var(--spacing) * 44);
    }

    .data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
        width: calc(var(--spacing) * 1.5);
    }

    .data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
        width: auto;
    }

    .data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
        width: 100%;
    }

    .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
        width: 1px;
    }

    .data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
        flex-direction: column;
    }

    .data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
        height: 1px;
    }

    .data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
        width: 100%;
    }

    .data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
        flex-direction: column;
    }

    .data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
        content: var(--tw-content);
        left: calc(var(--spacing) * 0);
    }

    .data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
        content: var(--tw-content);
        height: calc(var(--spacing) * 1);
    }

    .data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
        content: var(--tw-content);
        width: 100%;
    }

    .data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
        content: var(--tw-content);
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
        content: var(--tw-content);
        --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
        color: var(--muted-foreground);
    }

    .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
        background-color: var(--accent);
    }

    .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
        color: var(--accent-foreground);
    }

    .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
        --tw-translate-y: calc(var(--spacing) * 1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
        --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
    }

    .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
        --tw-translate-x: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
        --tw-enter-translate-x: calc(2 * var(--spacing));
    }

    .data-\[side\=right\]\:translate-x-1[data-side="right"] {
        --tw-translate-x: calc(var(--spacing) * 1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
        --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
    }

    .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
        --tw-translate-y: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
        --tw-enter-translate-y: calc(2 * var(--spacing));
    }

    .data-\[size\=default\]\:h-9[data-size="default"] {
        height: calc(var(--spacing) * 9);
    }

    .data-\[size\=sm\]\:h-8[data-size="sm"] {
        height: calc(var(--spacing) * 8);
    }

    :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
        color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
            color: color-mix(in oklab, var(--destructive) 90%, transparent);
        }
    }

    :is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
        height: calc(var(--spacing) * 12);
    }

    :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot="navigation-menu-link"]:focus {
        --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot="navigation-menu-link"]:focus {
        --tw-outline-style: none;
        outline-style: none;
    }

    :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
    }

    :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
        display: flex;
    }

    :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
        align-items: center;
    }

    :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
        gap: calc(var(--spacing) * 2);
    }

    .data-\[state\=active\]\:bg-card[data-state="active"] {
        background-color: var(--card);
    }

    .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
        --tw-translate-x: calc(100% - 2px);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[state\=checked\]\:border-community-teal[data-state="checked"] {
        border-color: var(--community-teal);
    }

    .data-\[state\=checked\]\:border-primary[data-state="checked"] {
        border-color: var(--primary);
    }

    .data-\[state\=checked\]\:bg-community-teal[data-state="checked"] {
        background-color: var(--community-teal);
    }

    .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
        background-color: var(--primary);
    }

    .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
        color: var(--primary-foreground);
    }

    .data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
        animation: accordion-up var(--tw-duration, .2s) ease-out;
    }

    .data-\[state\=closed\]\:animate-out[data-state="closed"] {
        animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[state\=closed\]\:duration-300[data-state="closed"] {
        --tw-duration: .3s;
        transition-duration: .3s;
    }

    .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
        --tw-exit-opacity: 0;
    }

    .data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
        --tw-exit-translate-y: 100%;
    }

    .data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
        --tw-exit-translate-x: -100%;
    }

    .data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
        --tw-exit-translate-x: 100%;
    }

    .data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
        --tw-exit-translate-y: -100%;
    }

    .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
        --tw-exit-scale: .95;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
        animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
        --tw-exit-opacity: 0;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
        --tw-exit-scale: .95;
    }

    .data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
        animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
        --tw-exit-opacity: 0;
    }

    .data-\[state\=on\]\:bg-accent[data-state="on"] {
        background-color: var(--accent);
    }

    .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
        color: var(--accent-foreground);
    }

    .data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
        animation: accordion-down var(--tw-duration, .2s) ease-out;
    }

    .data-\[state\=open\]\:animate-in[data-state="open"] {
        animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[state\=open\]\:bg-accent[data-state="open"] {
        background-color: var(--accent);
    }

    .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
        background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
            background-color: color-mix(in oklab, var(--accent) 50%, transparent);
        }
    }

    .data-\[state\=open\]\:bg-secondary[data-state="open"] {
        background-color: var(--secondary);
    }

    .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
        color: var(--accent-foreground);
    }

    .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
        color: var(--muted-foreground);
    }

    .data-\[state\=open\]\:opacity-100[data-state="open"] {
        opacity: 1;
    }

    .data-\[state\=open\]\:duration-500[data-state="open"] {
        --tw-duration: .5s;
        transition-duration: .5s;
    }

    .data-\[state\=open\]\:fade-in-0[data-state="open"] {
        --tw-enter-opacity: 0;
    }

    .data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
        --tw-enter-translate-y: 100%;
    }

    .data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
        --tw-enter-translate-x: -100%;
    }

    .data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
        --tw-enter-translate-x: 100%;
    }

    .data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
        --tw-enter-translate-y: -100%;
    }

    .data-\[state\=open\]\:zoom-in-90[data-state="open"] {
        --tw-enter-scale: .9;
    }

    .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
        --tw-enter-scale: .95;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
        animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
        --tw-enter-opacity: 0;
    }

    .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
        --tw-enter-scale: .95;
    }

    @media (hover: hover) {
        .data-\[state\=open\]\:hover\:bg-accent[data-state="open"]:hover {
            background-color: var(--accent);
        }
    }

    @media (hover: hover) {
        .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
            background-color: var(--sidebar-accent);
        }
    }

    @media (hover: hover) {
        .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
            color: var(--sidebar-accent-foreground);
        }
    }

    .data-\[state\=open\]\:focus\:bg-accent[data-state="open"]:focus {
        background-color: var(--accent);
    }

    .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
        background-color: var(--muted);
    }

    .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .data-\[state\=unchecked\]\:bg-switch-background[data-state="unchecked"] {
        background-color: var(--switch-background);
    }

    .data-\[state\=visible\]\:animate-in[data-state="visible"] {
        animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
    }

    .data-\[state\=visible\]\:fade-in[data-state="visible"] {
        --tw-enter-opacity: 0;
    }

    .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
        color: var(--destructive);
    }

    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
        background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
            background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
        }
    }

    .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
        color: var(--destructive);
    }

    .data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
        border-left-style: var(--tw-border-style);
        border-left-width: 0;
    }

    .data-\[variant\=outline\]\:shadow-xs[data-variant="outline"] {
        --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .data-\[variant\=outline\]\:first\:border-l[data-variant="outline"]:first-child {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
    }

    .data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction="bottom"] {
        inset-inline: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction="bottom"] {
        bottom: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction="bottom"] {
        margin-top: calc(var(--spacing) * 24);
    }

    .data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction="bottom"] {
        max-height: 80vh;
    }

    .data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction="bottom"] {
        border-top-left-radius: var(--radius);
        border-top-right-radius: var(--radius);
    }

    .data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction="bottom"] {
        border-top-style: var(--tw-border-style);
        border-top-width: 1px;
    }

    .data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction="left"] {
        inset-block: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction="left"] {
        left: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction="left"] {
        width: 75%;
    }

    .data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction="left"] {
        border-right-style: var(--tw-border-style);
        border-right-width: 1px;
    }

    .data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction="right"] {
        inset-block: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction="right"] {
        right: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction="right"] {
        width: 75%;
    }

    .data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction="right"] {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
    }

    .data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction="top"] {
        inset-inline: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction="top"] {
        top: calc(var(--spacing) * 0);
    }

    .data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction="top"] {
        margin-bottom: calc(var(--spacing) * 24);
    }

    .data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction="top"] {
        max-height: 80vh;
    }

    .data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction="top"] {
        border-bottom-right-radius: var(--radius);
        border-bottom-left-radius: var(--radius);
    }

    .data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction="top"] {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
    }

    @supports ((-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))) {
        .supports-\[backdrop-filter\]\:bg-white\/60 {
            background-color: #fff9;
        }

        @supports (color: color-mix(in lab, red, red)) {
            .supports-\[backdrop-filter\]\:bg-white\/60 {
                background-color: color-mix(in oklab, var(--color-white) 60%, transparent);
            }
        }
    }

    @media (width >= 40rem) {
        .sm\:block {
            display: block;
        }
    }

    @media (width >= 40rem) {
        .sm\:flex {
            display: flex;
        }
    }

    @media (width >= 40rem) {
        .sm\:max-w-lg {
            max-width: var(--container-lg);
        }
    }

    @media (width >= 40rem) {
        .sm\:max-w-sm {
            max-width: var(--container-sm);
        }
    }

    @media (width >= 40rem) {
        .sm\:flex-row {
            flex-direction: row;
        }
    }

    @media (width >= 40rem) {
        .sm\:justify-end {
            justify-content: flex-end;
        }
    }

    @media (width >= 40rem) {
        .sm\:gap-2\.5 {
            gap: calc(var(--spacing) * 2.5);
        }
    }

    @media (width >= 40rem) {
        .sm\:px-6 {
            padding-inline: calc(var(--spacing) * 6);
        }
    }

    @media (width >= 40rem) {
        .sm\:pr-2\.5 {
            padding-right: calc(var(--spacing) * 2.5);
        }
    }

    @media (width >= 40rem) {
        .sm\:pl-2\.5 {
            padding-left: calc(var(--spacing) * 2.5);
        }
    }

    @media (width >= 40rem) {
        .sm\:text-left {
            text-align: left;
        }
    }

    @media (width >= 40rem) {
        .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction="left"] {
            max-width: var(--container-sm);
        }
    }

    @media (width >= 40rem) {
        .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction="right"] {
            max-width: var(--container-sm);
        }
    }

    @media (width >= 48rem) {
        .md\:absolute {
            position: absolute;
        }
    }

    @media (width >= 48rem) {
        .md\:col-span-2 {
            grid-column: span 2 / span 2;
        }
    }

    @media (width >= 48rem) {
        .md\:mx-8 {
            margin-inline: calc(var(--spacing) * 8);
        }
    }

    @media (width >= 48rem) {
        .md\:mx-12 {
            margin-inline: calc(var(--spacing) * 12);
        }
    }

    @media (width >= 48rem) {
        .md\:mt-0 {
            margin-top: calc(var(--spacing) * 0);
        }
    }

    @media (width >= 48rem) {
        .md\:block {
            display: block;
        }
    }

    @media (width >= 48rem) {
        .md\:flex {
            display: flex;
        }
    }

    @media (width >= 48rem) {
        .md\:hidden {
            display: none;
        }
    }

    @media (width >= 48rem) {
        .md\:h-10 {
            height: calc(var(--spacing) * 10);
        }
    }

    @media (width >= 48rem) {
        .md\:h-12 {
            height: calc(var(--spacing) * 12);
        }
    }

    @media (width >= 48rem) {
        .md\:h-14 {
            height: calc(var(--spacing) * 14);
        }
    }

    @media (width >= 48rem) {
        .md\:h-20 {
            height: calc(var(--spacing) * 20);
        }
    }

    @media (width >= 48rem) {
        .md\:w-20 {
            width: calc(var(--spacing) * 20);
        }
    }

    @media (width >= 48rem) {
        .md\:w-24 {
            width: calc(var(--spacing) * 24);
        }
    }

    @media (width >= 48rem) {
        .md\:w-28 {
            width: calc(var(--spacing) * 28);
        }
    }

    @media (width >= 48rem) {
        .md\:w-40 {
            width: calc(var(--spacing) * 40);
        }
    }

    @media (width >= 48rem) {
        .md\:w-96 {
            width: calc(var(--spacing) * 96);
        }
    }

    @media (width >= 48rem) {
        .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
            width: var(--radix-navigation-menu-viewport-width);
        }
    }

    @media (width >= 48rem) {
        .md\:w-auto {
            width: auto;
        }
    }

    @media (width >= 48rem) {
        .md\:grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }

    @media (width >= 48rem) {
        .md\:grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
        }
    }

    @media (width >= 48rem) {
        .md\:grid-cols-4 {
            grid-template-columns: repeat(4, minmax(0, 1fr));
        }
    }

    @media (width >= 48rem) {
        .md\:grid-rows-2 {
            grid-template-rows: repeat(2, minmax(0, 1fr));
        }
    }

    @media (width >= 48rem) {
        .md\:flex-row {
            flex-direction: row;
        }
    }

    @media (width >= 48rem) {
        .md\:justify-start {
            justify-content: flex-start;
        }
    }

    @media (width >= 48rem) {
        .md\:py-32 {
            padding-block: calc(var(--spacing) * 32);
        }
    }

    @media (width >= 48rem) {
        .md\:text-2xl {
            font-size: var(--text-2xl);
            line-height: var(--tw-leading, var(--text-2xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-3xl {
            font-size: var(--text-3xl);
            line-height: var(--tw-leading, var(--text-3xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-4xl {
            font-size: var(--text-4xl);
            line-height: var(--tw-leading, var(--text-4xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-5xl {
            font-size: var(--text-5xl);
            line-height: var(--tw-leading, var(--text-5xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-6xl {
            font-size: var(--text-6xl);
            line-height: var(--tw-leading, var(--text-6xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-base {
            font-size: var(--text-base);
            line-height: var(--tw-leading, var(--text-base--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-sm {
            font-size: var(--text-sm);
            line-height: var(--tw-leading, var(--text-sm--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:text-xl {
            font-size: var(--text-xl);
            line-height: var(--tw-leading, var(--text-xl--line-height));
        }
    }

    @media (width >= 48rem) {
        .md\:opacity-0 {
            opacity: 0;
        }
    }

    @media (width >= 48rem) {
        .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
            margin: calc(var(--spacing) * 2);
        }
    }

    @media (width >= 48rem) {
        .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
            margin-left: calc(var(--spacing) * 0);
        }
    }

    @media (width >= 48rem) {
        .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
            border-radius: calc(var(--radius)  + 4px);
        }
    }

    @media (width >= 48rem) {
        .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
            --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
    }

    @media (width >= 48rem) {
        .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
            margin-left: calc(var(--spacing) * 2);
        }
    }

    @media (width >= 48rem) {
        .md\:after\:hidden:after {
            content: var(--tw-content);
            display: none;
        }
    }

    @media (width >= 64rem) {
        .lg\:col-span-2 {
            grid-column: span 2 / span 2;
        }
    }

    @media (width >= 64rem) {
        .lg\:col-span-3 {
            grid-column: span 3 / span 3;
        }
    }

    @media (width >= 64rem) {
        .lg\:row-span-2 {
            grid-row: span 2 / span 2;
        }
    }

    @media (width >= 64rem) {
        .lg\:mx-10 {
            margin-inline: calc(var(--spacing) * 10);
        }
    }

    @media (width >= 64rem) {
        .lg\:mx-16 {
            margin-inline: calc(var(--spacing) * 16);
        }
    }

    @media (width >= 64rem) {
        .lg\:mr-4 {
            margin-right: calc(var(--spacing) * 4);
        }
    }

    @media (width >= 64rem) {
        .lg\:mb-6 {
            margin-bottom: calc(var(--spacing) * 6);
        }
    }

    @media (width >= 64rem) {
        .lg\:block {
            display: block;
        }
    }

    @media (width >= 64rem) {
        .lg\:h-5 {
            height: calc(var(--spacing) * 5);
        }
    }

    @media (width >= 64rem) {
        .lg\:h-12 {
            height: calc(var(--spacing) * 12);
        }
    }

    @media (width >= 64rem) {
        .lg\:h-14 {
            height: calc(var(--spacing) * 14);
        }
    }

    @media (width >= 64rem) {
        .lg\:h-16 {
            height: calc(var(--spacing) * 16);
        }
    }

    @media (width >= 64rem) {
        .lg\:h-24 {
            height: calc(var(--spacing) * 24);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-5 {
            width: calc(var(--spacing) * 5);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-24 {
            width: calc(var(--spacing) * 24);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-28 {
            width: calc(var(--spacing) * 28);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-32 {
            width: calc(var(--spacing) * 32);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-48 {
            width: calc(var(--spacing) * 48);
        }
    }

    @media (width >= 64rem) {
        .lg\:w-\[28rem\] {
            width: 28rem;
        }
    }

    @media (width >= 64rem) {
        .lg\:grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }

    @media (width >= 64rem) {
        .lg\:grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
        }
    }

    @media (width >= 64rem) {
        .lg\:grid-cols-4 {
            grid-template-columns: repeat(4, minmax(0, 1fr));
        }
    }

    @media (width >= 64rem) {
        .lg\:grid-cols-5 {
            grid-template-columns: repeat(5, minmax(0, 1fr));
        }
    }

    @media (width >= 64rem) {
        .lg\:flex-row {
            flex-direction: row;
        }
    }

    @media (width >= 64rem) {
        .lg\:items-start {
            align-items: flex-start;
        }
    }

    @media (width >= 64rem) {
        .lg\:justify-start {
            justify-content: flex-start;
        }
    }

    @media (width >= 64rem) {
        .lg\:gap-2 {
            gap: calc(var(--spacing) * 2);
        }
    }

    @media (width >= 64rem) {
        :where(.lg\:space-y-4 > :not(:last-child)) {
            --tw-space-y-reverse: 0;
            margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
            margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
        }
    }

    @media (width >= 64rem) {
        .lg\:p-8 {
            padding: calc(var(--spacing) * 8);
        }
    }

    @media (width >= 64rem) {
        .lg\:px-8 {
            padding-inline: calc(var(--spacing) * 8);
        }
    }

    @media (width >= 64rem) {
        .lg\:py-24 {
            padding-block: calc(var(--spacing) * 24);
        }
    }

    @media (width >= 64rem) {
        .lg\:text-left {
            text-align: left;
        }
    }

    @media (width >= 64rem) {
        .lg\:text-2xl {
            font-size: var(--text-2xl);
            line-height: var(--tw-leading, var(--text-2xl--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-3xl {
            font-size: var(--text-3xl);
            line-height: var(--tw-leading, var(--text-3xl--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-4xl {
            font-size: var(--text-4xl);
            line-height: var(--tw-leading, var(--text-4xl--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-6xl {
            font-size: var(--text-6xl);
            line-height: var(--tw-leading, var(--text-6xl--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-base {
            font-size: var(--text-base);
            line-height: var(--tw-leading, var(--text-base--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-lg {
            font-size: var(--text-lg);
            line-height: var(--tw-leading, var(--text-lg--line-height));
        }
    }

    @media (width >= 64rem) {
        .lg\:text-xl {
            font-size: var(--text-xl);
            line-height: var(--tw-leading, var(--text-xl--line-height));
        }
    }

    @media (width >= 80rem) {
        .xl\:grid-cols-5 {
            grid-template-columns: repeat(5, minmax(0, 1fr));
        }
    }

    .dark\:border-input:is(.dark *) {
        border-color: var(--input);
    }

    .dark\:bg-destructive\/60:is(.dark *) {
        background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:bg-destructive\/60:is(.dark *) {
            background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
        }
    }

    .dark\:bg-input\/30:is(.dark *) {
        background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:bg-input\/30:is(.dark *) {
            background-color: color-mix(in oklab, var(--input) 30%, transparent);
        }
    }

    .dark\:text-muted-foreground:is(.dark *) {
        color: var(--muted-foreground);
    }

    @media (hover: hover) {
        .dark\:hover\:bg-accent\/50:is(.dark *):hover {
            background-color: var(--accent);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .dark\:hover\:bg-accent\/50:is(.dark *):hover {
                background-color: color-mix(in oklab, var(--accent) 50%, transparent);
            }
        }
    }

    @media (hover: hover) {
        .dark\:hover\:bg-input\/50:is(.dark *):hover {
            background-color: var(--input);
        }

        @supports (color: color-mix(in lab, red, red)) {
            .dark\:hover\:bg-input\/50:is(.dark *):hover {
                background-color: color-mix(in oklab, var(--input) 50%, transparent);
            }
        }
    }

    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
    }

    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
    }

    .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
    }

    .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
        border-color: var(--input);
    }

    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
        background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
            background-color: color-mix(in oklab, var(--input) 30%, transparent);
        }
    }

    .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
        color: var(--foreground);
    }

    .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
        background-color: var(--primary);
    }

    .dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
        background-color: var(--primary-foreground);
    }

    .dark\:data-\[state\=unchecked\]\:bg-card-foreground:is(.dark *)[data-state="unchecked"] {
        background-color: var(--card-foreground);
    }

    .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
        background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
            background-color: color-mix(in oklab, var(--input) 80%, transparent);
        }
    }

    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
        background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
    }

    .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
        fill: var(--muted-foreground);
    }

    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
        stroke: var(--border);
    }

    @supports (color: color-mix(in lab, red, red)) {
        .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
            stroke: color-mix(in oklab, var(--border) 50%, transparent);
        }
    }

    .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
        stroke: var(--border);
    }

    .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
        stroke: #0000;
    }

    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
        stroke: var(--border);
    }

    .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
        fill: var(--muted);
    }

    .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
        fill: var(--muted);
    }

    .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
        stroke: var(--border);
    }

    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
        stroke: #0000;
    }

    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
        --tw-outline-style: none;
        outline-style: none;
    }

    @media (forced-colors: active) {
        .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
            outline-offset: 2px;
            outline: 2px solid #0000;
        }
    }

    .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
        padding-inline: calc(var(--spacing) * 2);
    }

    .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
        padding-block: calc(var(--spacing) * 1.5);
    }

    .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
    }

    .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
    }

    .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
        color: var(--muted-foreground);
    }

    .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
        padding-inline: calc(var(--spacing) * 2);
    }

    .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
        padding-top: calc(var(--spacing) * 0);
    }

    .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
        height: calc(var(--spacing) * 5);
    }

    .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
        width: calc(var(--spacing) * 5);
    }

    .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
        height: calc(var(--spacing) * 12);
    }

    .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
        padding-inline: calc(var(--spacing) * 2);
    }

    .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
        padding-block: calc(var(--spacing) * 3);
    }

    .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
        height: calc(var(--spacing) * 5);
    }

    .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
        width: calc(var(--spacing) * 5);
    }

    .\[\&_p\]\:leading-relaxed p {
        --tw-leading: var(--leading-relaxed);
        line-height: var(--leading-relaxed);
    }

    .\[\&_strong\]\:text-brand-navy strong {
        color: var(--brand-navy);
    }

    .\[\&_svg\]\:pointer-events-none svg {
        pointer-events: none;
    }

    .\[\&_svg\]\:shrink-0 svg {
        flex-shrink: 0;
    }

    .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
    }

    .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
        color: var(--muted-foreground);
    }

    .\[\&_tr\]\:border-b tr {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
    }

    .\[\&_tr\:last-child\]\:border-0 tr:last-child {
        border-style: var(--tw-border-style);
        border-width: 0;
    }

    .\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
        border-top-right-radius: calc(var(--radius)  - 2px);
        border-bottom-right-radius: calc(var(--radius)  - 2px);
    }

    .\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
        border-top-left-radius: calc(var(--radius)  - 2px);
        border-bottom-left-radius: calc(var(--radius)  - 2px);
    }

    .\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
        border-radius: calc(var(--radius)  - 2px);
    }

    .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
        background-color: var(--accent);
    }

    .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
        border-top-left-radius: calc(var(--radius)  - 2px);
        border-bottom-left-radius: calc(var(--radius)  - 2px);
    }

    .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
        border-top-right-radius: calc(var(--radius)  - 2px);
        border-bottom-right-radius: calc(var(--radius)  - 2px);
    }

    .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
        border-top-right-radius: calc(var(--radius)  - 2px);
        border-bottom-right-radius: calc(var(--radius)  - 2px);
    }

    .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
        padding-right: calc(var(--spacing) * 0);
    }

    .\[\.border-b\]\:pb-6.border-b {
        padding-bottom: calc(var(--spacing) * 6);
    }

    .\[\.border-t\]\:pt-6.border-t {
        padding-top: calc(var(--spacing) * 6);
    }

    :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
        display: flex;
    }

    :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
        align-items: center;
    }

    :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
        gap: calc(var(--spacing) * 2);
    }

    :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
        color: var(--destructive) !important;
    }

    .\[\&\:last-child\]\:pb-6:last-child {
        padding-bottom: calc(var(--spacing) * 6);
    }

    .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
        --tw-translate-y: 2px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .\[\&\>button\]\:hidden > button {
        display: none;
    }

    .\[\&\>span\:last-child\]\:truncate > span:last-child {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .\[\&\>svg\]\:pointer-events-none > svg {
        pointer-events: none;
    }

    .\[\&\>svg\]\:size-3 > svg {
        width: calc(var(--spacing) * 3);
        height: calc(var(--spacing) * 3);
    }

    .\[\&\>svg\]\:size-3\.5 > svg {
        width: calc(var(--spacing) * 3.5);
        height: calc(var(--spacing) * 3.5);
    }

    .\[\&\>svg\]\:size-4 > svg {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
    }

    .\[\&\>svg\]\:h-2\.5 > svg {
        height: calc(var(--spacing) * 2.5);
    }

    .\[\&\>svg\]\:h-3 > svg {
        height: calc(var(--spacing) * 3);
    }

    .\[\&\>svg\]\:w-2\.5 > svg {
        width: calc(var(--spacing) * 2.5);
    }

    .\[\&\>svg\]\:w-3 > svg {
        width: calc(var(--spacing) * 3);
    }

    .\[\&\>svg\]\:shrink-0 > svg {
        flex-shrink: 0;
    }

    .\[\&\>svg\]\:translate-y-0\.5 > svg {
        --tw-translate-y: calc(var(--spacing) * .5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
    }

    .\[\&\>svg\]\:text-current > svg {
        color: currentColor;
    }

    .\[\&\>svg\]\:text-muted-foreground > svg {
        color: var(--muted-foreground);
    }

    .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
        color: var(--sidebar-accent-foreground);
    }

    .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 0;
    }

    .\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"] > div {
        rotate: 90deg;
    }

    .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
        rotate: 180deg;
    }

    [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
        right: calc(var(--spacing) * -2);
    }

    [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
        cursor: e-resize;
    }

    [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
        left: calc(var(--spacing) * -2);
    }

    [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
        cursor: w-resize;
    }

    @media (hover: hover) {
        a.\[a\&\]\:hover\:bg-accent:hover {
            background-color: var(--accent);
        }
    }

    @media (hover: hover) {
        a.\[a\&\]\:hover\:bg-destructive\/90:hover {
            background-color: var(--destructive);
        }

        @supports (color: color-mix(in lab, red, red)) {
            a.\[a\&\]\:hover\:bg-destructive\/90:hover {
                background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        a.\[a\&\]\:hover\:bg-primary\/90:hover {
            background-color: var(--primary);
        }

        @supports (color: color-mix(in lab, red, red)) {
            a.\[a\&\]\:hover\:bg-primary\/90:hover {
                background-color: color-mix(in oklab, var(--primary) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        a.\[a\&\]\:hover\:bg-secondary\/90:hover {
            background-color: var(--secondary);
        }

        @supports (color: color-mix(in lab, red, red)) {
            a.\[a\&\]\:hover\:bg-secondary\/90:hover {
                background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
            }
        }
    }

    @media (hover: hover) {
        a.\[a\&\]\:hover\:text-accent-foreground:hover {
            color: var(--accent-foreground);
        }
    }

    .text-brand-navy {
        color: var(--color-brand-navy);
    }

    .text-brand-red {
        color: var(--color-brand-red);
    }

    .text-brand-light-gray {
        color: var(--color-brand-light-gray);
    }

    .text-brand-purple {
        color: var(--color-brand-purple);
    }

    .text-brand-orange {
        color: var(--color-brand-orange);
    }

    .text-brand-teal {
        color: var(--color-brand-teal);
    }

    .bg-brand-navy {
        background-color: var(--color-brand-navy);
    }

    .bg-brand-red {
        background-color: var(--color-brand-red);
    }

    .bg-brand-light-gray {
        background-color: var(--color-brand-light-gray);
    }

    .bg-brand-purple {
        background-color: var(--color-brand-purple);
    }

    .bg-brand-orange {
        background-color: var(--color-brand-orange);
    }

    .bg-brand-teal {
        background-color: var(--color-brand-teal);
    }

    .border-brand-navy {
        border-color: var(--color-brand-navy);
    }

    .border-brand-red {
        border-color: var(--color-brand-red);
    }

    .border-brand-light-gray {
        border-color: var(--color-brand-light-gray);
    }

    .border-brand-purple {
        border-color: var(--color-brand-purple);
    }

    .border-brand-orange {
        border-color: var(--color-brand-orange);
    }

    .border-brand-teal {
        border-color: var(--color-brand-teal);
    }

    .text-community-teal {
        color: var(--color-community-teal);
    }

    .text-community-purple {
        color: var(--color-community-purple);
    }

    .text-community-coral {
        color: var(--color-community-coral);
    }

    .text-community-sky-blue {
        color: var(--color-community-sky-blue);
    }

    .text-community-golden-yellow {
        color: var(--color-community-golden-yellow);
    }

    .text-community-navy {
        color: var(--color-community-navy);
    }

    .text-community-neutral-gray {
        color: var(--color-community-neutral-gray);
    }

    .text-community-dark-blue {
        color: var(--color-community-dark-blue);
    }

    .bg-community-teal {
        background-color: var(--color-community-teal);
    }

    .bg-community-purple {
        background-color: var(--color-community-purple);
    }

    .bg-community-coral {
        background-color: var(--color-community-coral);
    }

    .bg-community-sky-blue {
        background-color: var(--color-community-sky-blue);
    }

    .bg-community-golden-yellow {
        background-color: var(--color-community-golden-yellow);
    }

    .bg-community-navy {
        background-color: var(--color-community-navy);
    }

    .bg-community-neutral-gray {
        background-color: var(--color-community-neutral-gray);
    }

    .bg-community-dark-blue {
        background-color: var(--color-community-dark-blue);
    }

    .border-community-teal {
        border-color: var(--color-community-teal);
    }

    .border-community-purple {
        border-color: var(--color-community-purple);
    }

    .border-community-coral {
        border-color: var(--color-community-coral);
    }

    .border-community-sky-blue {
        border-color: var(--color-community-sky-blue);
    }

    .border-community-golden-yellow {
        border-color: var(--color-community-golden-yellow);
    }

    .border-community-navy {
        border-color: var(--color-community-navy);
    }

    .border-community-neutral-gray {
        border-color: var(--color-community-neutral-gray);
    }

    .border-community-dark-blue {
        border-color: var(--color-community-dark-blue);
    }

    .text-academy-teal {
        color: var(--color-academy-teal);
    }

    .text-academy-purple {
        color: var(--color-academy-purple);
    }

    .bg-academy-teal {
        background-color: var(--color-academy-teal);
    }

    .bg-academy-purple {
        background-color: var(--color-academy-purple);
    }

    .border-academy-teal {
        border-color: var(--color-academy-teal);
    }

    .border-academy-purple {
        border-color: var(--color-academy-purple);
    }

    .from-academy-teal {
        --tw-gradient-from: var(--color-academy-teal) var(--tw-gradient-from-position);
    }

    .via-academy-purple {
        --tw-gradient-via: var(--color-academy-purple) var(--tw-gradient-via-position);
    }

    .to-academy-teal {
        --tw-gradient-to: var(--color-academy-teal) var(--tw-gradient-to-position);
    }

    .font-stolzl {
        font-family: Stolzl, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif;
    }
}



:root {
    --font-size: 14px;
    --background: #fff;
    --foreground: #070664;
    --card: #fff;
    --card-foreground: #070664;
    --popover: #fff;
    --popover-foreground: #070664;
    --primary: #95278b;
    --primary-foreground: #fff;
    --secondary: #ebeafc;
    --secondary-foreground: #070664;
    --muted: #ebeafc;
    --muted-foreground: #163f55;
    --accent: #eb1b45;
    --accent-foreground: #fff;
    --destructive: #eb1b45;
    --destructive-foreground: #fff;
    --border: #0706641a;
    --input: transparent;
    --input-background: #ebeafc;
    --switch-background: #163f55;
    --font-weight-medium: 500;
    --font-weight-normal: 400;
    --ring: #95278b;
    --chart-1: #95278b;
    --chart-2: #eb1b45;
    --chart-3: #95278b;
    --chart-4: #ff7a49;
    --chart-5: #163f55;
    --radius: .625rem;
    --sidebar: #ebeafc;
    --sidebar-foreground: #070664;
    --sidebar-primary: #95278b;
    --sidebar-primary-foreground: #fff;
    --sidebar-accent: #fff;
    --sidebar-accent-foreground: #070664;
    --sidebar-border: #0706641a;
    --sidebar-ring: #95278b;
    --brand-navy: #070664;
    --brand-red: #eb1b45;
    --brand-light-gray: #ebeafc;
    --brand-purple: #95278b;
    --brand-orange: #ff7a49;
    --brand-teal: #163f55;
    --community-teal: #00b2a9;
    --community-purple: #802788;
    --community-coral: #ff8960;
    --community-sky-blue: #8fdfeb;
    --community-golden-yellow: #f4c842;
    --community-navy: #070664;
    --community-neutral-gray: #d7d2cb;
    --community-dark-blue: #163f55;
    --academy-teal: #00b2a9;
    --academy-purple: #802788;
}

.dark {
    --background: #070664;
    --foreground: #fff;
    --card: #070664;
    --card-foreground: #fff;
    --popover: #070664;
    --popover-foreground: #fff;
    --primary: #95278b;
    --primary-foreground: #fff;
    --secondary: #163f55;
    --secondary-foreground: #fff;
    --muted: #163f55;
    --muted-foreground: #ebeafc;
    --accent: #163f55;
    --accent-foreground: #fff;
    --destructive: #eb1b45;
    --destructive-foreground: #fff;
    --border: #ffffff1a;
    --input: #ffffff1a;
    --ring: #95278b;
    --font-weight-medium: 500;
    --font-weight-normal: 400;
    --chart-1: #fff;
    --chart-2: #eb1b45;
    --chart-3: #95278b;
    --chart-4: #ff7a49;
    --chart-5: #ebeafc;
    --sidebar: #070664;
    --sidebar-foreground: #fff;
    --sidebar-primary: #eb1b45;
    --sidebar-primary-foreground: #fff;
    --sidebar-accent: #163f55;
    --sidebar-accent-foreground: #fff;
    --sidebar-border: #ffffff1a;
    --sidebar-ring: #95278b;
}

html {
    font-size: var(--font-size);
}

@keyframes scroll-left {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-50%);
    }
}

.animate-scroll-left {
    width: fit-content;
    animation: 30s linear infinite scroll-left;
}

.animate-scroll-left:hover {
    animation-play-state: paused;
}

@media (width <= 768px) {
    .animate-scroll-left {
        animation-duration: 25s;
    }
}

@media (width <= 480px) {
    .animate-scroll-left {
        animation-duration: 20s;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeIn {
    animation: .5s ease-out forwards fadeIn;
}

@property --tw-translate-x {
    syntax: "*";
    inherits: false;
    initial-value: 0;
}

@property --tw-translate-y {
    syntax: "*";
    inherits: false;
    initial-value: 0;
}

@property --tw-translate-z {
    syntax: "*";
    inherits: false;
    initial-value: 0;
}

@property --tw-scale-x {
    syntax: "*";
    inherits: false;
    initial-value: 1;
}

@property --tw-scale-y {
    syntax: "*";
    inherits: false;
    initial-value: 1;
}

@property --tw-scale-z {
    syntax: "*";
    inherits: false;
    initial-value: 1;
}

@property --tw-rotate-x {
    syntax: "*";
    inherits: false;
    initial-value: rotateX(0);
}

@property --tw-rotate-y {
    syntax: "*";
    inherits: false;
    initial-value: rotateY(0);
}

@property --tw-rotate-z {
    syntax: "*";
    inherits: false;
    initial-value: rotateZ(0);
}

@property --tw-skew-x {
    syntax: "*";
    inherits: false;
    initial-value: skewX(0);
}

@property --tw-skew-y {
    syntax: "*";
    inherits: false;
    initial-value: skewY(0);
}

@property --tw-space-y-reverse {
    syntax: "*";
    inherits: false;
    initial-value: 0;
}

@property --tw-space-x-reverse {
    syntax: "*";
    inherits: false;
    initial-value: 0;
}

@property --tw-border-style {
    syntax: "*";
    inherits: false;
    initial-value: solid;
}

@property --tw-gradient-position {
    syntax: "*";
    inherits: false
}

@property --tw-gradient-from {
    syntax: "<color>";
    inherits: false;
    initial-value: #0000;
}

@property --tw-gradient-via {
    syntax: "<color>";
    inherits: false;
    initial-value: #0000;
}

@property --tw-gradient-to {
    syntax: "<color>";
    inherits: false;
    initial-value: #0000;
}

@property --tw-gradient-stops {
    syntax: "*";
    inherits: false
}

@property --tw-gradient-via-stops {
    syntax: "*";
    inherits: false
}

@property --tw-gradient-from-position {
    syntax: "<length-percentage>";
    inherits: false;
    initial-value: 0%;
}

@property --tw-gradient-via-position {
    syntax: "<length-percentage>";
    inherits: false;
    initial-value: 50%;
}

@property --tw-gradient-to-position {
    syntax: "<length-percentage>";
    inherits: false;
    initial-value: 100%;
}

@property --tw-leading {
    syntax: "*";
    inherits: false
}

@property --tw-font-weight {
    syntax: "*";
    inherits: false
}

@property --tw-tracking {
    syntax: "*";
    inherits: false
}

@property --tw-ordinal {
    syntax: "*";
    inherits: false
}

@property --tw-slashed-zero {
    syntax: "*";
    inherits: false
}

@property --tw-numeric-figure {
    syntax: "*";
    inherits: false
}

@property --tw-numeric-spacing {
    syntax: "*";
    inherits: false
}

@property --tw-numeric-fraction {
    syntax: "*";
    inherits: false
}

@property --tw-shadow {
    syntax: "*";
    inherits: false;
    initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
    syntax: "*";
    inherits: false
}

@property --tw-shadow-alpha {
    syntax: "<percentage>";
    inherits: false;
    initial-value: 100%;
}

@property --tw-inset-shadow {
    syntax: "*";
    inherits: false;
    initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
    syntax: "*";
    inherits: false
}

@property --tw-inset-shadow-alpha {
    syntax: "<percentage>";
    inherits: false;
    initial-value: 100%;
}

@property --tw-ring-color {
    syntax: "*";
    inherits: false
}

@property --tw-ring-shadow {
    syntax: "*";
    inherits: false;
    initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
    syntax: "*";
    inherits: false
}

@property --tw-inset-ring-shadow {
    syntax: "*";
    inherits: false;
    initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
    syntax: "*";
    inherits: false
}

@property --tw-ring-offset-width {
    syntax: "<length>";
    inherits: false;
    initial-value: 0;
}

@property --tw-ring-offset-color {
    syntax: "*";
    inherits: false;
    initial-value: #fff;
}

@property --tw-ring-offset-shadow {
    syntax: "*";
    inherits: false;
    initial-value: 0 0 #0000;
}

@property --tw-outline-style {
    syntax: "*";
    inherits: false;
    initial-value: solid;
}

@property --tw-blur {
    syntax: "*";
    inherits: false
}

@property --tw-brightness {
    syntax: "*";
    inherits: false
}

@property --tw-contrast {
    syntax: "*";
    inherits: false
}

@property --tw-grayscale {
    syntax: "*";
    inherits: false
}

@property --tw-hue-rotate {
    syntax: "*";
    inherits: false
}

@property --tw-invert {
    syntax: "*";
    inherits: false
}

@property --tw-opacity {
    syntax: "*";
    inherits: false
}

@property --tw-saturate {
    syntax: "*";
    inherits: false
}

@property --tw-sepia {
    syntax: "*";
    inherits: false
}

@property --tw-drop-shadow {
    syntax: "*";
    inherits: false
}

@property --tw-drop-shadow-color {
    syntax: "*";
    inherits: false
}

@property --tw-drop-shadow-alpha {
    syntax: "<percentage>";
    inherits: false;
    initial-value: 100%;
}

@property --tw-drop-shadow-size {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-blur {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-brightness {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-contrast {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-grayscale {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-hue-rotate {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-invert {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-opacity {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-saturate {
    syntax: "*";
    inherits: false
}

@property --tw-backdrop-sepia {
    syntax: "*";
    inherits: false
}

@property --tw-duration {
    syntax: "*";
    inherits: false
}

@property --tw-ease {
    syntax: "*";
    inherits: false
}

@property --tw-content {
    syntax: "*";
    inherits: false;
    initial-value: "";
}

@keyframes pulse {
    50% {
        opacity: .5;
    }
}

@keyframes enter {
    from {
        opacity: var(--tw-enter-opacity, 1);
        transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
    }
}

@keyframes exit {
    to {
        opacity: var(--tw-exit-opacity, 1);
        transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
    }
}

@keyframes accordion-down {
    from {
        height: 0;
    }

    to {
        height: var(--radix-accordion-content-height, var(--bits-accordion-content-height));
    }
}

@keyframes accordion-up {
    from {
        height: var(--radix-accordion-content-height, var(--bits-accordion-content-height));
    }

    to {
        height: 0;
    }
}

@keyframes caret-blink {
    0%, 70%, 100% {
        opacity: 1;
    }

    20%, 50% {
        opacity: 0;
    }
}
