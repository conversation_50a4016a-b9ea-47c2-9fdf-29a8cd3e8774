import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "../components/ui/card";
import { Linkedin } from "lucide-react";
import { Link } from "react-router-dom";
import { ImageWithFallback } from '../components/figma/ImageWithFallback';
import { DifferentiatorsAccordion } from "../components/DifferentiatorsAccordion";
import { TrustSignals } from "../components/TrustSignals";
import bennettPortrait from '/public/asset/a6b84e1efdfb8b80ddbe9cb686df6d67bad7bb58.png';
import aaronRiekePhoto from '/public/asset/e0be01c505893f57e2f49439053976f795f73b96.png';
import jaredConleyPhoto from '/public/asset/041680be9086865395576f8f1be16db407e3483c.png';
import shellySkinnerPhoto from '/public/asset/e1c6fa22e2a049013d11712dc03024edc7cd8ebd.png';
import bobBordenPhoto from '/public/asset/58733a3c4db886c55ec92e6cff9c53556985db08.png';
import chrisCullenPhoto from '/public/asset/12456c040d6adadee10303eac13d4ed5c1a43ea3.png';
import jessThurstonPhoto from '/public/asset/9c7d7045dcad3d2eb17b750c477f598e0492dfa9.png';
import cathyONeilPhoto from '/public/asset/d03147c785db936c3b40a3a1438008414ffb7174.png';
import jacobAppelPhoto from '/public/asset/2ba496f29f177f22b2626c6421bb05ba1187b0e1.png';
import robertMahariPhoto from '/public/asset/9b44c4a84297345f1e6d09d7aa20f75064ad7527.png';
import nickHafenPhoto from '/public/asset/b4db04819816cade1908644b19ddb2ca0e8d6a9a.png';
import rachaelHutchingsPhoto from '/public/asset/3e72d94eced04637e4ab31e50bd14e264ef551ab.png';

export default function AboutPage() {
    const [bennettExpanded, setBennettExpanded] = useState(false);
    const [aaronExpanded, setAaronExpanded] = useState(false);

    return (
        <div className="bg-white">
            {/* Hero Section - Our Story */}
            <section className="py-16 lg:py-24 bg-slate-50">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="relative">
                        <div className="mb-8">
                            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-brand-navy leading-tight">Our story</h1>
                        </div>

                        <div className="text-brand-navy space-y-6">
                            <p className="text-gray-800">In 2024, Bennett Borden left his role as a partner, AI Practice Group head, and Chief Data Scientist at one of the world's largest law firms to launch Clarion AI Partners. While leading at Big Law, Bennett saw that the challenges and opportunities of artificial intelligence required a new kind of organization—one built to unite law, technology, and business strategy in a single practice.</p>

                            <p className="text-gray-800">Clarion was created with that vision. We are a team of lawyers, data scientists, AI engineers, and business strategists who solve real business problems, design strategies, and help organizations build AI solutions with confidence.</p>

                            <div className="text-gray-800">
                                <p className="mb-4">We strive to:</p>
                                <ul className="ml-4">
                                    <li className="flex items-start mb-2">
                                        <span className="text-brand-red mr-3">•</span>
                                        <span>Help businesses of every size advance responsibly with AI</span>
                                    </li>
                                    <li className="flex items-start mb-2">
                                        <span className="text-brand-red mr-3">•</span>
                                        <span>Ensure that AI benefits society broadly while protecting against risks</span>
                                    </li>
                                    <li className="flex items-start mb-2">
                                        <span className="text-brand-red mr-3">•</span>
                                        <span>Provide practical safeguards that encourage innovation and adoption</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-brand-red mr-3">•</span>
                                        <span>Guide leaders through the legal, technical, and organizational complexity of AI's transformation</span>
                                    </li>
                                </ul>
                            </div>

                            <p className="text-gray-800">Clarion continues to bring together top talent from across industries and disciplines, strengthened by partnerships with academia, business, and government. This unique blend equips us to help organizations move forward with clarity, expertise, and ethical grounding.</p>

                            <p className="text-gray-800">We invite you to meet the Clarion team and explore how we can shape an AI future that works for everyone.</p>
                        </div>

                        <div className="mt-8">
                            <Link to="/lets-talk">
                                <Button className="bg-brand-red text-white border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red">
                                    Let's talk
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            {/* AI Academy Section */}

            {/* Trust Signals Section */}
            <TrustSignals />

            {/* Team Section */}
            <section id="team" className="py-16 lg:py-24 bg-white">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl lg:text-4xl text-brand-navy mb-4">Meet the Clarion team</h2>
                    </div>

                    {/* Interactive Team Tabs */}
                    <div className="bg-slate-50 rounded-2xl p-8">
                        {/* Leadership Section */}
                        <div className="space-y-8 mb-8">
                            {/* Bennett Borden */}
                            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                                <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
                                    <div className="flex-shrink-0">
                                        <img
                                            src={bennettPortrait}
                                            alt="Bennett B. Borden"
                                            className="w-32 h-32 rounded-full object-cover shadow-lg"
                                        />
                                    </div>
                                    <div className="flex-1 text-center lg:text-left">
                                        <div className="flex items-center justify-center lg:justify-start gap-3 mb-2">
                                            <h4 className="text-brand-navy">Bennett Borden</h4>
                                            <a
                                                href="https://www.linkedin.com/in/bennettborden/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1.5 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-4 h-4 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-4">Founder and CEO</p>
                                        <div className="text-brand-navy space-y-3">
                                            <p className="text-gray-800">
                                                Bennett B. Borden is a globally recognized authority on the legal, technological, and policy implications of artificial intelligence. As a Big Law partner, Bennett focused on helping clients use the power of AI to drive strategic outcomes and improve business operations.
                                            </p>
                                            {bennettExpanded && (
                                                <div className="space-y-3 animate-fadeIn">
                                                    <p className="text-gray-800">
                                                        Bennett is a unique AI visionary leader. Only a handful of people in the world share his credentials as an AI lawyer-data scientist-AI ethicist. With his special blend of professional disciplines and insight, Bennett has made significant contributions to the field of AI governance and algorithmic bias testing.
                                                    </p>
                                                    <p className="text-gray-800">
                                                        He has long standing connections to public service and public policy. From his days in US Intelligence, to recent roles in developing federal and state artificial intelligence policy and legislation, Bennett remains fascinated with how electronic information reflects human thought, choice, and conduct.
                                                    </p>
                                                    <p className="text-gray-800">
                                                        Anyone who spends five minutes with CEO Bennett marvels at his friendliness and mastery of turning the complex into the simple. His commitment to promoting constitutional rights, human dignity, and access to justice remain the core of his professional identity.
                                                    </p>
                                                </div>
                                            )}
                                            <Button
                                                variant="outline"
                                                onClick={() => setBennettExpanded(!bennettExpanded)}
                                                className="border-brand-red text-brand-red hover:bg-brand-red hover:text-white mt-4"
                                            >
                                                {bennettExpanded ? 'Show less' : 'Show more'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Aaron Rieke */}
                            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                                <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
                                    <div className="flex-shrink-0">
                                        <ImageWithFallback
                                            src={aaronRiekePhoto}
                                            alt="Aaron Rieke"
                                            className="w-32 h-32 rounded-full object-cover shadow-lg"
                                        />
                                    </div>
                                    <div className="flex-1 text-center lg:text-left">
                                        <div className="flex items-center justify-center lg:justify-start gap-3 mb-2">
                                            <h4 className="text-brand-navy">Aaron Rieke</h4>
                                            <a
                                                href="https://www.linkedin.com/in/aaron-rieke-5a178b28/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1.5 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-4 h-4 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-4">Senior Partner</p>
                                        <div className="text-brand-navy space-y-3">
                                            <p className="text-gray-800">
                                                Aaron Rieke, our Senior Partner based in Washington, DC, is a veteran lawyer-technologist. Aaron has served at the Federal Trade Commission, advised the White House and Congress on AI policy, and published high-impact computer science research.
                                            </p>
                                            {aaronExpanded && (
                                                <div className="space-y-3 animate-fadeIn">
                                                    <p className="text-gray-800">
                                                        Aaron's work has been widely recognized in the popular press, including the New York Times, the Washington Post, the Wall Street Journal, the Harvard Business Review, the Economist, and other major publications.
                                                    </p>
                                                </div>
                                            )}
                                            <Button
                                                variant="outline"
                                                onClick={() => setAaronExpanded(!aaronExpanded)}
                                                className="border-brand-red text-brand-red hover:bg-brand-red hover:text-white mt-4"
                                            >
                                                {aaronExpanded ? 'Show less' : 'Show more'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Executive Team Section */}
                        <div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {/* Chris Cullen */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={chrisCullenPhoto}
                                                alt="Chris Cullen"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Chris Cullen</h5>
                                            <a
                                                href="https://www.linkedin.com/in/christopherpcullen/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">AI Strategy Partner, Director of The Foundry</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Chris, an exceptional lawyer-data scientist, heads our Foundry. He is an expert in building AI engines that ensure algorithmic performance, compliance, and fairness. Chris leads our team of AI developers who are building innovative AI systems with designed-in performance and compliance testing for Clarion AI clients.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Jess Thurston */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={jessThurstonPhoto}
                                                alt="Jess Thurston"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Jess Thurston</h5>
                                            <a
                                                href="https://www.linkedin.com/in/jessamyn-thurston/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">Chief of Staff</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Jess Thurston is a master of organizational maturity. As a certified Agile Scrum Master, Jess excels at organizational, project, and people management. She brings her considerable leadership experience from several different industries to Clarion AI Partners.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Bob Borden */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={bobBordenPhoto}
                                                alt="Bob Borden"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Bob Borden</h5>
                                            <a
                                                href="https://www.linkedin.com/in/robert-borden-45a2b8180/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">Chief Strategy Officer</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Bob is the strategic visionary behind Clarion's growth and market positioning. With extensive experience in business development and strategic planning, he helps guide the company's long-term direction and partnership initiatives.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Jared Conley */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={jaredConleyPhoto}
                                                alt="Jared Conley"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300 bg-white"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Jared Conley</h5>
                                            <a
                                                href="https://www.linkedin.com/in/jaredwconley/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">Chief Marketing Officer</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Jared has a passion for data, technology, and creating meaningful value for customers. Over the past 20 years, he has helped grow technology companies by staying focused on the customer and driven by data. He specializes in modernizing go-to-market strategies to accelerate growth.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Shelly Skinner */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={shellySkinnerPhoto}
                                                alt="Shelly Skinner"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Shelly Skinner</h5>
                                            <a
                                                href="https://www.linkedin.com/in/shellyskinner/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">Director of AI Governance and Strategy</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Shelly earned a JD at the University of Texas before serving as Special Ethics Counsel to the NLRB. She is an expert in technology ethics, especially the ethics of artificial intelligence. She is committed to increasing access to justice, while protecting privacy and ensuring transparency.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Nick Hafen */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={nickHafenPhoto}
                                                alt="Nick Hafen"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Nick Hafen, JD</h5>
                                            <a
                                                href="https://www.linkedin.com/in/ndhafen/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">AI Strategist</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Nick earned a JD from Brigham Young University, before returning to head their Legal Technology Education Department. He currently leads the efforts there to create the country's premier legal technology training and development program, including LawX, the Law School's legal design lab.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Rachael Hutchings */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={rachaelHutchingsPhoto}
                                                alt="Rachael Hutchings"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Rachael Hutchings</h5>
                                            <a
                                                href="https://www.linkedin.com/in/rachaelhutchings/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">AI Transformation Strategist</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Holding a JD from the University of Memphis, Rachael is an expert in strategic AI and business foresight with extensive consulting experience both in the U.S. and abroad. She currently leads as the Executive Director of the Applied Artificial Intelligence Institute at Utah Valley University. She is also a Professional in Residence in the Strategic Management and Operations Department at the Woodbury School of Business.
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>

                        {/* Certified Partners Section */}
                        <div className="mt-12">
                            <h3 className="text-brand-navy mb-8 text-center">Certified Partners</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {/* Cathy O'Neil */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={cathyONeilPhoto}
                                                alt="Cathy O'Neil"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Cathy O'Neil</h5>
                                        </div>
                                        <p className="text-brand-red mb-1">Director of Data Science</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Cathy is widely recognized for her expertise in machine learning and data analysis. She is a highly respected data scientist, author, and advocate for ethical AI and data practices. With a Ph.D. in Mathematics from Harvard University, Cathy's diverse background bridges academia and industry, from MIT to Wall Street.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Jacob Appel */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={jacobAppelPhoto}
                                                alt="Jacob Appel"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Jacob Appel</h5>
                                            <a
                                                href="https://www.linkedin.com/in/jakeappel/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">Senior Data Scientist</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            With an MPA from Princeton, Jacob conducts algorithmic audits, and specializes in designing tests and analyses to assess the performance of algorithms and their impacts on stakeholders. He is a published author who has worked with state and local governments in behavioral science and public policies and programs.
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Robert Mahari */}
                                <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="text-center pb-1">
                                        <div className="mb-4">
                                            <ImageWithFallback
                                                src={robertMahariPhoto}
                                                alt="Robert Mahari"
                                                className="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:scale-105 transition-transform duration-300"
                                            />
                                        </div>
                                        <div className="flex items-center justify-center gap-2 mb-1">
                                            <h5 className="text-brand-navy">Robert Mahari</h5>
                                            <a
                                                href="https://www.linkedin.com/in/robert-mahari/"
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="p-1 rounded-full bg-[#0077B5] hover:bg-[#004182] transition-colors duration-200"
                                            >
                                                <Linkedin className="w-3 h-3 text-white" />
                                            </a>
                                        </div>
                                        <p className="text-brand-red mb-1">AI Policy & Governance Fellow</p>
                                    </CardHeader>
                                    <CardContent className="text-center">
                                        <p className="text-gray-800">
                                            Robert is a PhD student in the Human Dynamics group at MIT and a JD candidate at Harvard Law School. He specializes on the intersection of tech and law with a focus on increasing access to justice and judicial efficacy.
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* What Sets Us Apart Section */}
            <DifferentiatorsAccordion />
        </div>
    );
}
