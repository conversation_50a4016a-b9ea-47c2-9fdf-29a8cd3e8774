import { Button } from "../components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "../components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "../components/ui/accordion";
import { Scale, Shield, Users, Globe, Zap, CheckCircle, ArrowRight, BookOpen, Gavel } from "lucide-react";

export default function DifferentiatorsDemo() {
  const differentiators = [
    {
      icon: Scale,
      title: "Lawyers who are also data scientists",
      description: "Our leadership combines legal experience with technical knowledge. We speak both languages fluently: compliance and code.",
      iconColor: "text-brand-red",
      bgColor: "bg-brand-red/10"
    },
    {
      icon: Shield,
      title: "Governance built in",
      description: "We integrate legal, ethical, and policy safeguards into AI systems from the start. That way, you can innovate without compromising trust.",
      iconColor: "text-brand-purple",
      bgColor: "bg-brand-purple/10"
    },
    {
      icon: BookOpen,
      title: "Policy insight from the inside",
      description: "Our team includes former government advisors, including from the FTC. We understand how regulators think because we have been regulators ourselves.",
      iconColor: "text-brand-teal",
      bgColor: "bg-brand-teal/10"
    },
    {
      icon: Gavel,
      title: "We help shape policy, not just respond to it",
      description: "From advising the White House to contributing to national legislation, we are directly involved in shaping the future of AI regulation.",
      iconColor: "text-brand-orange",
      bgColor: "bg-brand-orange/10"
    },
    {
      icon: Globe,
      title: "Cross-functional and cross-industry",
      description: "We support clients in technology, finance, healthcare, manufacturing, public service, and beyond. Our work is tailored to the unique risks and realities of each field.",
      iconColor: "text-brand-navy",
      bgColor: "bg-brand-navy/10"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <section className="py-12 px-4 bg-brand-navy">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl lg:text-4xl mb-4 text-white">
            Differentiators Layout Demo
          </h1>
          <p className="text-xl text-white/80">
            Exploring different ways to showcase our 5 key competitive advantages
          </p>
        </div>
      </section>

      {/* Layout 1: Traditional Cards */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 1: Traditional Cards
          </h2>
          <p className="text-brand-teal text-center mb-12">Classic card layout with icons and hover effects</p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {differentiators.map((item, index) => (
              <Card key={index} className="border-2 border-brand-light-gray hover:border-brand-red transition-colors group h-full">
                <CardHeader>
                  <div className={`w-12 h-12 ${item.bgColor} rounded-lg flex items-center justify-center mb-4 group-hover:bg-brand-red group-hover:text-white transition-colors`}>
                    <item.icon className={`h-6 w-6 ${item.iconColor} group-hover:text-white`} />
                  </div>
                  <CardTitle className="text-lg text-brand-navy">
                    {item.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-brand-teal leading-relaxed">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Layout 2: Icon-Based Grid */}
      <section className="py-16 px-4 bg-brand-light-gray">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 2: Icon-Based Grid
          </h2>
          <p className="text-brand-teal text-center mb-12">Larger icons with centered text layout</p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {differentiators.map((item, index) => (
              <div key={index} className="text-center group">
                <div className={`w-20 h-20 ${item.bgColor} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                  <item.icon className={`h-10 w-10 ${item.iconColor}`} />
                </div>
                <h3 className="text-xl text-brand-navy mb-4">
                  {item.title}
                </h3>
                <p className="text-brand-teal leading-relaxed">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Layout 3: Timeline Format */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 3: Timeline Format
          </h2>
          <p className="text-brand-teal text-center mb-12">Vertical timeline showing progression of capabilities</p>
          
          <div className="space-y-8">
            {differentiators.map((item, index) => (
              <div key={index} className="relative flex items-start">
                {/* Timeline line */}
                {index < differentiators.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-16 bg-brand-light-gray"></div>
                )}
                
                {/* Icon circle */}
                <div className={`w-12 h-12 ${item.bgColor} rounded-full flex items-center justify-center flex-shrink-0 border-4 border-white shadow-lg`}>
                  <item.icon className={`h-6 w-6 ${item.iconColor}`} />
                </div>
                
                {/* Content */}
                <div className="ml-6 flex-1">
                  <h3 className="text-xl text-brand-navy mb-2">
                    {item.title}
                  </h3>
                  <p className="text-brand-teal leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Layout 4: Accordion Style */}
      <section className="py-16 px-4 bg-brand-light-gray">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 4: Accordion Style
          </h2>
          <p className="text-brand-teal text-center mb-12">Interactive expandable format for detailed exploration</p>
          
          <Accordion type="multiple" className="space-y-4">
            {differentiators.map((item, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="bg-white rounded-lg border border-brand-light-gray">
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center mr-4`}>
                      <item.icon className={`h-5 w-5 ${item.iconColor}`} />
                    </div>
                    <span className="text-lg text-brand-navy">{item.title}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  <p className="text-brand-teal leading-relaxed ml-14">{item.description}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Layout 5: Split Content Layout */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 5: Split Content Layout
          </h2>
          <p className="text-brand-teal text-center mb-12">Alternating left-right layout with visual emphasis</p>
          
          <div className="space-y-16">
            {differentiators.map((item, index) => (
              <div key={index} className={`flex items-center ${index % 2 === 1 ? 'flex-row-reverse' : ''} gap-12`}>
                <div className="flex-1">
                  <div className={`w-16 h-16 ${item.bgColor} rounded-xl flex items-center justify-center mb-6`}>
                    <item.icon className={`h-8 w-8 ${item.iconColor}`} />
                  </div>
                  <h3 className="text-2xl text-brand-navy mb-4">
                    {item.title}
                  </h3>
                  <p className="text-lg text-brand-teal leading-relaxed">{item.description}</p>
                </div>
                <div className="flex-1">
                  <div className={`h-64 ${item.bgColor} rounded-xl flex items-center justify-center`}>
                    <item.icon className={`h-24 w-24 ${item.iconColor}`} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Layout 6: Minimal List Format */}
      <section className="py-16 px-4 bg-brand-light-gray">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl lg:text-3xl mb-2 text-brand-navy text-center">
            Layout 6: Minimal List Format
          </h2>
          <p className="text-brand-teal text-center mb-12">Clean, text-focused approach with subtle visual elements</p>
          
          <div className="space-y-8">
            {differentiators.map((item, index) => (
              <div key={index} className="flex items-start group hover:bg-white rounded-lg p-6 transition-colors">
                <div className="flex-shrink-0 mr-6">
                  <div className={`w-2 h-2 ${item.iconColor.replace('text-', 'bg-')} rounded-full mt-3`}></div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl text-brand-navy mb-3 group-hover:text-brand-red transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-brand-teal leading-relaxed">{item.description}</p>
                </div>
                <ArrowRight className="h-5 w-5 text-brand-light-gray group-hover:text-brand-red transition-colors opacity-0 group-hover:opacity-100 mt-2" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Navigation Helper */}
      <section className="py-12 px-4 bg-brand-navy">
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-white/80 mb-6">
            You can access this demo page at /differentiators-demo
          </p>
          <Button 
            onClick={() => window.location.href = '/'}
            className="bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300"
          >
            Back to Home
          </Button>
        </div>
      </section>
    </div>
  );
}