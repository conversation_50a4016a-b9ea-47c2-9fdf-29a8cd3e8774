import { useState, FC, useCallback, useEffect } from 'react';
import { useGoogleReCaptcha } from '../components/use-google-recaptcha';
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import { TrustSignals } from '../components/TrustSignals';
import heroBackgroundImage from '/public/asset/e85ac521e75b19838cdde3fc9f66114d840c59a5.png';

import { GoogleReCaptchaProvider } from '../components/google-recaptcha-provider';
import { GoogleRecaptcha } from '../components/GoogleRecaptcha';

export default function LetsTalkPage() {
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        company: '',
        phone: '',
        jobTitle: '',
        message: ''
    });

    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [captchaToken, setCaptchaToken] = useState('');

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        console.log(captchaToken);
        const response = await fetch('/api/contact/submit',{method: "POST",
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name: formData.firstName + " " + formData.lastName, email: formData.email, company: formData.company, phone: formData.phone, title: formData.jobTitle, message: formData.message })
        })

        // Simulate form submission
        //await new Promise(resolve => setTimeout(resolve, 1000));

        setIsSubmitted(true);
        setIsSubmitting(false);
    };

    const isEmailValid = formData.email.includes('@') && formData.email.length > 0;
    const canSubmit = isEmailValid && formData.firstName && formData.lastName;

    if (isSubmitted) {
        return (
            <div className="bg-white">
                {/* Thank You Hero Section */}
                <section className="py-16 lg:py-24 bg-white">
                    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <h1 className="text-5xl lg:text-6xl text-brand-navy mb-6">Thank you</h1>
                        <p className="text-xl lg:text-2xl text-brand-navy leading-relaxed">
                            Clarion will be in touch very soon.
                        </p>
                    </div>
                </section>

                {/* Trust Reinforcement Section */}
                <section className="py-16 bg-slate-50">
                    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <blockquote className="text-2xl lg:text-3xl text-brand-navy italic mb-4">
                            "Clarion helped us align AI pilots to real business goals in weeks, not months."
                        </blockquote>
                        <p className="text-lg text-gray-600">- Leading entertainment and media group</p>
                    </div>
                </section>

                {/* Customer Logo Scroll */}
                <TrustSignals />
            </div>
        );
    }

    return (
        <div className="bg-white">
            {/* Hero Section */}
            <section className="py-16 lg:py-24 relative bg-white">
                {/* Background Image with Opacity */}
                <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-15"
                    style={{backgroundImage: `url(${heroBackgroundImage})`}}
                />
                {/* Content */}
                <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-5xl lg:text-6xl text-brand-navy mb-6">Let's Talk</h1>
                    <h2 className="text-xl lg:text-2xl text-brand-navy mb-8">
                        Our experts are here to guide you through the complete AI lifecycle.
                    </h2>
                    <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
                        Every AI journey is unique. At Clarion AI Partners, we bring together lawyers, technologists,
                        and strategists to help enterprises adopt AI responsibly and effectively. From strategy and
                        roadmaps, to custom builds, to ongoing risk management — we're here to guide you through the
                        complete lifecycle. Let's talk about how we can help your business succeed with AI.
                    </p>
                </div>
            </section>

            {/* Contact Form Section */}
            <section className="py-16 bg-slate-50">
                <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-8 rounded-2xl shadow-lg">
                        {/* First Name and Last Name */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <div id="captcha"></div>
                                <Label htmlFor="firstName" className="text-brand-navy">First Name</Label>
                                <Input
                                    id="firstName"
                                    name="firstName"
                                    type="text"
                                    value={formData.firstName}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="lastName" className="text-brand-navy">Last Name</Label>
                                <Input
                                    id="lastName"
                                    name="lastName"
                                    type="text"
                                    value={formData.lastName}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                    required
                                />
                            </div>
                        </div>

                        {/* Email and Company */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="email" className="text-brand-navy">
                                    Email <span className="text-brand-red">*</span>
                                </Label>
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="company" className="text-brand-navy">Company</Label>
                                <Input
                                    id="company"
                                    name="company"
                                    type="text"
                                    value={formData.company}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                />
                            </div>
                        </div>

                        {/* Phone and Job Title */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="phone" className="text-brand-navy">Phone</Label>
                                <Input
                                    id="phone"
                                    name="phone"
                                    type="tel"
                                    value={formData.phone}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="jobTitle" className="text-brand-navy">Job Title</Label>
                                <Input
                                    id="jobTitle"
                                    name="jobTitle"
                                    type="text"
                                    value={formData.jobTitle}
                                    onChange={handleInputChange}
                                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                                />
                            </div>
                        </div>

                        {/* Message */}
                        <div className="space-y-2">
                            <Label htmlFor="message" className="text-brand-navy">How can we help?</Label>
                            <Textarea
                                id="message"
                                name="message"
                                value={formData.message}
                                onChange={handleInputChange}
                                placeholder="Optional: Tell us more about what you are looking for."
                                className="min-h-[200px] bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red resize-none"
                            />
                        </div>
                        <div id="html_element"></div>
                        {/* Submit Button */}
                        <div className="flex justify-end">
                            <Button
                                type="submit"
                                disabled={!canSubmit || isSubmitting}
                                className="bg-brand-red border border-brand-red text-white hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300 px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isSubmitting ? 'Sending...' : 'Submit'}
                            </Button>
                        </div>
                    </form>
                </div>
            </section>

            {/* Trust Reinforcement Section */}
            <section className="py-16 bg-white">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <blockquote className="text-2xl lg:text-3xl text-brand-navy italic mb-4">
                        "Clarion helped us align AI pilots to real business goals in weeks, not months."
                    </blockquote>
                    <p className="text-lg text-gray-600">- Leading entertainment and media group</p>
                </div>
            </section>
            {captchaToken}
            {/* Customer Logo Scroll */}
            <TrustSignals/>
            <GoogleReCaptchaProvider
                useRecaptchaNet
                reCaptchaKey="6LciIswrAAAAAJ73CTlVr-xP4T0kXKQQGkvHBnbv"
                scriptProps={{ async: true, defer: true, appendTo: 'body' }}
                onVerify={setCaptchaToken}
            >
                <h2>Google Recaptcha Example</h2>
                    <GoogleRecaptcha />
                </GoogleReCaptchaProvider>
        </div>
    );
}
