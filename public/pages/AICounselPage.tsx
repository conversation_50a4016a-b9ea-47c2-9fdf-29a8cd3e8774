import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Link } from "react-router-dom";
import { ImageWithFallback } from '../components/figma/ImageWithFallback';
import { services, caseExamples } from '../components/AICounselConstants';
import walmartLogo from '/public/asset/d57aebf7642b7163a1636cbed0954e70c53b81ae.png';
import larryMillerLogo from '/public/asset/ccf95b5a7535717f360d815736e379f55de0755c.png';
import kpmgLogo from '/public/asset/9732f0fbf666ca9738296f3bdd52dfdfb8a72580.png';
import grantThorntonLogo from '/public/asset/c132f2f79255402fd8afa5b8aaeb7141b71e2c66.png';
import methodLogo from '/public/asset/9590f62d2fd19db1d3cd7dc7304067b7c0a13ac7.png';
import familyDollarLogo from '/public/asset/ca99358a9753f48fa22c75351a4091b58c31c0ba.png';
import heroImage from '/public/asset/32a469083c83bde6bc79144e276a7fef8b73486d.png';
import bennettPortrait from '/public/asset/a6b84e1efdfb8b80ddbe9cb686df6d67bad7bb58.png';
import aaronRiekePhoto from '/public/asset/e0be01c505893f57e2f49439053976f795f73b96.png';

export default function AICounselPage() {
    const trustLogos = [
        { src: walmartLogo, alt: "Walmart", name: "walmart" },
        { src: larryMillerLogo, alt: "Larry H. Miller Company", name: "larry-miller" },
        { src: kpmgLogo, alt: "KPMG", name: "kpmg" },
        { src: grantThorntonLogo, alt: "Grant Thornton", name: "grant-thornton" },
        { src: methodLogo, alt: "Method", name: "method" },
        { src: familyDollarLogo, alt: "Family Dollar", name: "family-dollar" }
    ];

    return (
        <div className="min-h-screen bg-white">
            {/* Hero Section */}
            <section className="relative py-24 px-4 overflow-hidden">
                {/* Background Image */}
                <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
                    style={{ backgroundImage: `url(${heroImage})` }}
                />

                {/* Red/Blue Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-navy via-brand-red to-brand-navy opacity-1" />

                {/* Content */}
                <div className="max-w-4xl mx-auto px-4 text-center relative z-10">
                    <h1 className="text-4xl md:text-6xl font-bold mb-6 text-brand-navy leading-tight">
                        AI counsel you can trust
                    </h1>
                    <p className="text-xl md:text-2xl text-brand-teal mb-8 leading-relaxed">
                        Technically expert legal counsel for a fast-moving regulatory environment.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Link to="/lets-talk">
                            <Button
                                size="lg"
                                className="text-lg px-8 py-4 w-48 bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300"
                            >
                                Talk with our experts
                            </Button>
                        </Link>
                    </div>
                </div>
            </section>

            {/* Clarion's Legal Services */}
            <section id="services" className="py-16 bg-white">
                <div className="max-w-4xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl lg:text-4xl text-brand-navy mb-6">Comprehensive AI legal services</h2>
                    </div>

                    <div className="prose prose-lg max-w-none text-brand-teal mb-8">
                        <p className="text-base">
                            AI crosses many areas of law, including privacy, consumer protection, civil rights, and sector-specific regulations. Clarion helps its clients – including those in technology, financial services, employment, and health – adopt and govern AI in an effective, compliant, and ethical way.
                        </p>

                        <p className="mt-6 text-base">
                            Whether you are bringing AI products to market, introducing AI into your business, or looking to manage existing workflows, we can help you:
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {services.map((service, index) => {
                            const IconComponent = service.icon;
                            return (
                                <div key={index} className="flex items-start space-x-6 p-6 bg-brand-light-gray rounded-lg hover:shadow-lg transition-shadow duration-300">
                                    <div className="flex-shrink-0">
                                        <div className="w-12 h-12 bg-gradient-to-br from-brand-red to-brand-purple rounded-full flex items-center justify-center">
                                            <IconComponent className="w-6 h-6 text-white" />
                                        </div>
                                    </div>
                                    <div className="flex-grow">
                                        <p className="text-brand-teal text-base">{service.title}</p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </section>

            {/* Meet Our Principals */}
            <section className="py-16 bg-slate-50">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl lg:text-4xl text-brand-navy mb-4">Meet our principals</h2>
                        <p className="text-xl text-brand-teal">
                            Leading experts in AI law, technology, and policy
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 gap-8">
                        {/* Bennett B. Borden */}
                        <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                            <CardHeader className="text-center">
                                <div className="w-32 h-32 mx-auto mb-4 overflow-hidden rounded-full">
                                    <ImageWithFallback
                                        src={bennettPortrait}
                                        alt="Bennett B. Borden"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <CardTitle className="text-brand-navy">Bennett B. Borden</CardTitle>
                                <p className="text-brand-red">CEO & Founding Partner</p>
                            </CardHeader>
                            <CardContent className="text-center">
                                <p className="text-brand-teal text-base">
                                    Bennett is a global authority on the legal, technological, and policy implications of AI. Advised Fortune 500 clients as Chief Data Scientist and Partner at AmLaw100 firms DLA Piper and Faegre Drinker.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Aaron Rieke */}
                        <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                            <CardHeader className="text-center">
                                <div className="w-32 h-32 mx-auto mb-4 overflow-hidden rounded-full">
                                    <ImageWithFallback
                                        src={aaronRiekePhoto}
                                        alt="Aaron Rieke"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <CardTitle className="text-brand-navy">Aaron Rieke</CardTitle>
                                <p className="text-brand-red">Senior Partner, Washington DC</p>
                            </CardHeader>
                            <CardContent className="text-center">
                                <p className="text-brand-teal text-base">
                                    Aaron is a veteran lawyer-technologist. Served at the Federal Trade Commission, advised the White House, Congress, and leading technology companies on AI policy, and published high-impact computer science research.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Case Examples / Industries Served */}
            <section className="py-16 bg-white">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl lg:text-4xl text-brand-navy mb-4">Expertise across the industry spectrum</h2>
                        <p className="text-xl text-brand-teal">
                            Real-world solutions for complex AI legal challenges
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {caseExamples.map((example, index) => {
                            const Icon = example.icon
                            return (<Card key={index} className="bg-white border border-brand-light-gray hover:shadow-lg transition-shadow duration-300">
                                <CardHeader className="text-center pb-2">
                                    <div className="flex justify-center mb-3">
                                        <Icon className="h-8 w-8 text-brand-purple" />
                            </div>
                                    <CardTitle className="text-brand-navy">{example.industry}</CardTitle>
                                </CardHeader>
                                <CardContent className="text-center">
                                    <p className="text-brand-teal mb-4 text-base">
                                        {example.description}
                                    </p>
                                    <Badge variant="secondary" className="bg-brand-red text-white">
                                        {example.outcome}
                                    </Badge>
                                </CardContent>
                            </Card>
                        );
                        })}
                    </div>
                </div>
            </section>

            {/* Trust Indicators */}
            <section className="py-16 bg-white">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl lg:text-4xl text-brand-navy mb-4">Trusted by industry leaders</h2>
                        <p className="text-xl text-brand-teal">
                            Trusted by leaders across industries, from Fortune 500 innovators to government policymakers.
                        </p>
                    </div>
                </div>

                {/* Trust logos carousel - full width */}
                <div className="overflow-hidden relative">
                    <div className="flex animate-scroll-left">
                        {/* First set of logos */}
                        {trustLogos.map((logo, index) => (
                            <div
                                key={`first-${logo.name}-${index}`}
                                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
                            >
                                <div className="w-32 h-16 md:w-40 md:h-20 lg:w-48 lg:h-24 flex items-center justify-center">
                                    <ImageWithFallback
                                        src={logo.src}
                                        alt={logo.alt}
                                        className="w-full h-full object-contain"
                                    />
                                </div>
                            </div>
                        ))}

                        {/* Duplicate set for seamless loop */}
                        {trustLogos.map((logo, index) => (
                            <div
                                key={`second-${logo.name}-${index}`}
                                className="flex-shrink-0 mx-8 md:mx-12 lg:mx-16"
                            >
                                <div className="w-32 h-16 md:w-40 md:h-20 lg:w-48 lg:h-24 flex items-center justify-center">
                                    <ImageWithFallback
                                        src={logo.src}
                                        alt={logo.alt}
                                        className="w-full h-full object-contain"
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-brand-orange/90 text-white">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <h2 className="text-3xl lg:text-4xl mb-6 text-white">Ready to navigate AI's legal landscape with confidence?</h2>
                    <p className="text-xl text-white mb-8">
                        Let our expert legal team guide your AI initiatives with confidence and compliance.
                    </p>
                    <Link to="/lets-talk">
                        <Button
                            size="lg"
                            className="text-lg px-8 py-4 w-48 bg-white text-brand-orange hover:bg-slate-50 border border-white transition-colors duration-300"
                        >
                            Talk with our experts
                        </Button>
                    </Link>
                </div>
            </section>
        </div>
    );
}
