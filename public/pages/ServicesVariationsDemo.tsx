import { ServicesUnified } from "../components/ServicesUnified";
import { ServicesUnifiedVariation1 } from "../components/ServicesUnifiedVariation1";
import { ServicesUnifiedVariation2 } from "../components/ServicesUnifiedVariation2";
import { ServicesUnifiedVariation3 } from "../components/ServicesUnifiedVariation3";
import { ServicesUnifiedVariation4 } from "../components/ServicesUnifiedVariation4";

export default function ServicesVariationsDemo() {
  return (
    <div className="min-h-screen">
      {/* Original */}
      <div className="mb-16">
        <div className="bg-brand-navy text-white py-8">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold">Original Design</h1>
            <p className="text-brand-light-gray">Current services section</p>
          </div>
        </div>
        <ServicesUnified />
      </div>

      {/* Variation 1: Color-coded with badges */}
      <div className="mb-16">
        <div className="bg-brand-navy text-white py-8">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold">Variation 1: Color-coded Cards</h1>
            <p className="text-brand-light-gray">Each service has its own brand color, enhanced borders, and badges</p>
          </div>
        </div>
        <ServicesUnifiedVariation1 />
      </div>

      {/* Variation 2: Featured layout */}
      <div className="mb-16">
        <div className="bg-brand-navy text-white py-8">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold">Variation 2: Featured Service Layout</h1>
            <p className="text-brand-light-gray">One service is prominently featured as the main offering</p>
          </div>
        </div>
        <ServicesUnifiedVariation2 />
      </div>

      {/* Variation 3: Hover transformations */}
      <div className="mb-16">
        <div className="bg-brand-navy text-white py-8">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold">Variation 3: Interactive Hover Effects</h1>
            <p className="text-brand-light-gray">Dramatic color transformations on hover with stats badges</p>
          </div>
        </div>
        <ServicesUnifiedVariation3 />
      </div>

      {/* Variation 4: Feature-rich cards */}
      <div className="mb-16">
        <div className="bg-brand-navy text-white py-8">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold">Variation 4: Feature-rich Cards</h1>
            <p className="text-brand-light-gray">Detailed feature lists with gradient backgrounds</p>
          </div>
        </div>
        <ServicesUnifiedVariation4 />
      </div>
    </div>
  );
}