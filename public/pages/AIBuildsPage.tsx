import { <PERSON><PERSON> } from "../components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "../components/ui/card";
import { CheckCircle, Cog, Shield, BarChart3, Quote, UserCircle, } from "lucide-react";
import { Link } from "react-router-dom";
import { TrustSignals } from '../components/TrustSignals';
import { useState, useEffect } from 'react';

import heroImage from '/public/asset/4a42df648d3040480c97abc5e61f2f04c48f3c9b.png';

export default function AIBuildsPage() {

    const [currentBuildQuote, setCurrentBuildQuote] = useState(0);

    const solutions = [
        {
            icon: Cog,
            title: "Tailored AI solutions",
            description: "Custom-built AI for your workflows and customers",
            features: [
                "Custom-built AI for your workflows and customers",
                "Seamless integration with your existing data",
                "Phased deployment plans to avoid disruption and encourage adoption"
            ]
        },
        {
            icon: Shield,
            title: "AI oversight and risk frameworks",
            description: "AI use policies and risk controls",
            features: [
                "AI use policies and risk controls",
                "Clear roles and oversight models",
                "Executive and team training for safe scaling"
            ]
        },
        {
            icon: BarChart3,
            title: "Performance tools",
            description: "Live dashboards for AI usage and outcomes",
            features: [
                "Live dashboards for AI usage and outcomes",
                "Integrated performance and compliance testing",
                "Regulatory-ready documentation"
            ]
        }
    ];

    const buildSteps = [
        {
            title: "Discover",
            description: "We work with you to identify the right opportunities, assess risks, and design a strategy aligned with your goals."
        },
        {
            title: "Deliver",
            description: "Whether developed in-house or with trusted partners, we provide AI solutions tailored to your business that are cutting edge, compliant, and built for real-world impact."
        },
        {
            title: "Operationalize",
            description: "We configure dashboards, train your teams, and provide ongoing oversight so your AI scales responsibly and with confidence."
        }
    ];

    const targetAudiences = [
        "Teams <span class='font-bold text-brand-navy'>exploring AI</span> but needing structure and safeguards",
        "Companies <span class='font-bold text-brand-navy'>ready to launch</span> enterprise AI solutions",
        "<span class='font-bold text-brand-navy'>Internal AI development</span> teams requiring testing or oversight",
        "<span class='font-bold text-brand-navy'>Regulated industries</span> needing auditable AI compliance"
    ];



    const buildQuotes = [
        {
            quote: "Clarion didn't hand us a cookie-cutter solution. They built exactly what we needed for our business, our customers, and our industry.",
            author: "CEO",
            company: "SaaS Startup"
        },
        {
            quote: "Whether it's a quick win or a major enterprise build, Clarion delivers with the same level of expertise and attention.",
            author: "Chief Innovation Officer",
            company: "Healthcare System"
        },
        {
            quote: "Most firms talk about AI. Clarion builds it, launches it, and makes sure it runs right.",
            author: "CIO",
            company: "Manufacturing Enterprise"
        }
    ];



    // Rotate build quotes every 6 seconds (different timing to avoid sync)
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentBuildQuote((prev) => (prev + 1) % buildQuotes.length);
        }, 6000);
        return () => clearInterval(interval);
    }, [buildQuotes.length]);

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section className="relative py-24 px-4 bg-white overflow-hidden">

                {/* Blue/Purple Gradient Overlay at 3% opacity */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-navy via-brand-purple to-brand-navy opacity-[0.03]" />

                {/* Background Image - Using a tech/building themed image */}
                <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-18"
                    style={{ backgroundImage: `url(${heroImage})` }}
                />

                {/* Content */}
                <div className="max-w-4xl mx-auto relative z-10">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-brand-navy leading-tight">
                            From first model to full-scale AI — built right
                        </h1>
                        <p className="text-xl md:text-2xl mb-10 text-brand-teal leading-relaxed">
                            We deliver AI that fits your business: compliant, scalable, and built to last.
                        </p>
                        <Link to="/lets-talk">
                            <Button size="lg" className="text-lg px-8 py-4 w-48 bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300">
                                Let's build together
                            </Button>
                        </Link>
                    </div>
                </div>
            </section>

            {/* Trust Signals */}


            {/* Why AI Builds Section */}
            <section className="py-20 px-4 bg-white">
                <div className="max-w-4xl mx-auto">
                    <h2 className="text-3xl lg:text-4xl mb-8 text-brand-navy text-center">
                        Why leading enterprises choose Clarion AI builds
                    </h2>
                    <p className="text-xl text-brand-teal mb-12 text-center">
                        We design and deliver AI that drives growth, reduces risk, and earns trust across the business, from the boardroom to the front line.
                    </p>

                    <div className="grid md:grid-cols-3 gap-8 items-start">
                        <div className="md:col-span-2">
                            <div className="text-base text-gray-800 leading-relaxed">
                                <p className="mb-6">
                                    AI is transforming every industry, but most pilots fail to move beyond experimentation. Clarion bridges the gap between innovation and real-world results.
                                </p>
                                <ul className="space-y-4">
                                    <li className="flex items-start">
                                        <span className="text-brand-red mr-3 mt-1">•</span>
                                        <span>We combine deep technical expertise with legal and organizational insight to design AI that is <strong className="text-gray-800">auditable, compliant, and scalable</strong>.</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-brand-red mr-3 mt-1">•</span>
                                        <span>Our builds don't just launch. They <strong className="text-gray-800">deliver measurable ROI, accelerate adoption, and withstand regulatory scrutiny</strong>.</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-brand-red mr-3 mt-1">•</span>
                                        <span>Whether developed in-house or with trusted partners, Clarion solutions help you <strong className="text-gray-800">unlock new revenue, reduce operational costs, and win executive and employee confidence</strong>.</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div className="bg-brand-light-gray rounded-xl p-6 relative">
                            <Quote className="h-8 w-8 text-brand-red mb-4" />
                            <blockquote className="text-base text-brand-teal italic mb-4 leading-relaxed">
                                "With Clarion, we didn't have to choose between innovation and compliance. Their team combined deep technical expertise with legal insight to give us AI we could trust — and scale with confidence."
                            </blockquote>
                            <div className="flex items-center text-sm text-brand-navy">
                                <UserCircle className="h-6 w-6 text-brand-red mr-2 flex-shrink-0" />
                                <strong>General Counsel, Fortune 100 Retailer</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Trust Signals */}
            <TrustSignals />

            {/* What We Deliver Section */}
            <section className="py-20 px-4 bg-slate-50">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl lg:text-4xl mb-6 text-brand-navy">
                            For projects big and small
                        </h2>
                    </div>
                </div>

                <div className="max-w-6xl mx-auto px-4">
                    <div className="grid md:grid-cols-3 gap-8">
                        {solutions.map((solution, index) => (
                            <Card key={index} className="border-2 border-brand-red h-full bg-white flex flex-col">
                                <CardHeader className="flex-shrink-0">
                                    <div className="w-12 h-12 bg-brand-red rounded-lg flex items-center justify-center mb-4">
                                        <solution.icon className="h-6 w-6 text-white" />
                                    </div>
                                    <CardTitle className="text-xl text-brand-navy mb-2 flex items-start">
                                        {solution.title}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="flex-grow">
                                    <ul className="space-y-3">
                                        {solution.features.map((feature, featureIndex) => (
                                            <li key={featureIndex} className="flex items-start">
                                                <CheckCircle className="h-5 w-5 text-brand-red mt-0.5 mr-3 flex-shrink-0" />
                                                <span className="text-brand-teal leading-relaxed">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* How We Build Section */}
            <section className="py-20 px-4 bg-white">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl lg:text-4xl mb-6 text-brand-navy">
                            How we build with you
                        </h2>
                    </div>
                </div>

                <div className="max-w-4xl mx-auto px-4">
                    <div className="space-y-12">
                        {buildSteps.map((step, index) => (
                            <div key={index} className="border-l-4 border-brand-red pl-8 py-4">
                                <div className="flex items-start gap-4">
                  <span className="text-3xl font-bold text-brand-red mt-1">
                    {String(index + 1).padStart(2, '0')}
                  </span>
                                    <div className="flex-1">
                                        <h4 className="text-2xl text-brand-navy mb-3">{step.title}</h4>
                                        <p className="text-brand-teal leading-relaxed text-lg">
                                            {step.description}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Customer Quote */}
                    <div className="max-w-2xl mx-auto mt-16">
                        <div className="bg-slate-50 rounded-xl p-8 text-center min-h-32 flex flex-col justify-center">
                            <blockquote className="text-lg text-gray-800 italic mb-4 leading-relaxed">
                                "{buildQuotes[currentBuildQuote].quote}"
                            </blockquote>
                            <div className="text-brand-navy">
                                <strong>{buildQuotes[currentBuildQuote].author}, {buildQuotes[currentBuildQuote].company}</strong>
                            </div>

                            {/* Quote Indicators */}
                            <div className="flex justify-center space-x-2 mt-6">
                                {buildQuotes.map((_, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setCurrentBuildQuote(index)}
                                        className={`w-3 h-3 rounded-full transition-colors ${
                                            index === currentBuildQuote ? 'bg-brand-red' : 'bg-brand-teal/30'
                                        }`}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Who It's For Section */}
            <section className="py-20 px-4 bg-slate-50">
                <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-3xl lg:text-4xl mb-12 text-brand-navy">
                        Who we help
                    </h2>
                    <div className="grid md:grid-cols-2 gap-6 max-w-3xl mx-auto">
                        {targetAudiences.map((audience, index) => (
                            <div key={index} className="flex items-center justify-center md:justify-start">
                                <CheckCircle className="h-6 w-6 text-brand-red mr-4 flex-shrink-0" />
                                <span className="text-base text-brand-teal text-left" dangerouslySetInnerHTML={{ __html: audience }}></span>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Why Clarion Section */}
            <section className="py-20 px-4 bg-white">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl lg:text-4xl mb-8 text-brand-navy">
                            Why work with Clarion?
                        </h2>
                        <p className="text-lg text-brand-teal leading-relaxed mb-12">
                            We are a rare combination of AI engineering and legal expertise. Our team includes AI architects, data scientists, legal risk experts, and compliance leaders. Together, we design and deliver AI systems that are innovative, resilient, and trusted by executives and regulators alike.
                        </p>
                    </div>


                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 px-4 bg-brand-orange/90 text-white">
                <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-3xl lg:text-4xl mb-6 text-white">
                        Ready to build AI the right way?
                    </h2>
                    <p className="text-xl text-white mb-8 leading-relaxed">
                        Let's talk about how Clarion can help you turn AI into a competitive advantage with clarity, security, and trust.
                    </p>
                    <Link to="/lets-talk">
                        <Button size="lg" className="text-lg px-8 py-4 w-48 bg-white text-brand-orange hover:bg-slate-50 border border-white transition-colors duration-300 mb-6">
                            Let's build together
                        </Button>
                    </Link>
                    <p className="text-white text-lg">
                        Join Fortune 500 companies and innovative startups who trust Clarion for their AI success.
                    </p>
                </div>
            </section>
        </div>
    );
}
