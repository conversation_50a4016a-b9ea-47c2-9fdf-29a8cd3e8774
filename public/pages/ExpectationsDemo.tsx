import { ExpectationsIconVersion, ExpectationsCardVersion, ExpectationsCompactVersion } from '../components/ExpectationsVariations';
import { ExpectationsCurrentVersion } from '../components/ExpectationsCurrentVersion';

export default function ExpectationsDemo() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 py-16">
        <h1 className="text-4xl text-brand-navy text-center mb-16">
          "What you can expect" Section Variations
        </h1>

        {/* Variation 1: Icon-based with graphic element */}
        <section className="mb-20 border-b border-gray-200 pb-20">
          <h2 className="text-2xl text-brand-navy mb-8">
            Variation 1: Icon-based with graphic element (inspired by your image)
          </h2>
          <ExpectationsIconVersion />
        </section>

        {/* Variation 2: Horizontal card layout */}
        <section className="mb-20 border-b border-gray-200 pb-20">
          <h2 className="text-2xl text-brand-navy mb-8">
            Variation 2: Horizontal card layout
          </h2>
          <ExpectationsCardVersion />
        </section>

        {/* Variation 3: Compact list version */}
        <section className="mb-20 border-b border-gray-200 pb-20">
          <h2 className="text-2xl text-brand-navy mb-8">
            Variation 3: Compact list with cards
          </h2>
          <ExpectationsCompactVersion />
        </section>

        {/* Current style for comparison */}
        <section className="mb-20">
          <h2 className="text-2xl text-brand-navy mb-8">
            Current Style (for comparison)
          </h2>
          <ExpectationsCurrentVersion />
        </section>
      </div>
    </div>
  );
}