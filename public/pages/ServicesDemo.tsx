import { useState } from 'react';
import { <PERSON><PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "../components/ui/tabs";
import { 
  CheckCircle, 
  Scale, 
  Shield, 
  FileText, 
  Building, 
  Users, 
  Target,
  Gavel,
  BookOpen,
  AlertTriangle,
  CheckSquare
} from "lucide-react";

export default function ServicesDemo() {
  const [currentVariation, setCurrentVariation] = useState(1);

  const services = [
    {
      title: "Establish company-wide governance policies to keep your business on track and ahead of the curve",
      category: "governance",
      icon: Shield
    },
    {
      title: "Conduct audits, testing, and other technical assessments of AI systems",
      category: "assessment",
      icon: Target
    },
    {
      title: "Create procedures for identifying and approving AI projects within your organizations",
      category: "governance", 
      icon: CheckSquare
    },
    {
      title: "Vet potential developers, and draft appropriate vendor agreements",
      category: "contracts",
      icon: FileText
    },
    {
      title: "Ensure compliant AI deployments, incorporating expertise on a full range of laws and regulations",
      category: "compliance",
      icon: Scale
    },
    {
      title: "Stay informed about new developments in AI law and regulation",
      category: "advisory",
      icon: BookOpen
    }
  ];

  const categoryColors = {
    governance: "bg-brand-purple text-white",
    assessment: "bg-brand-orange text-white", 
    contracts: "bg-brand-teal text-white",
    compliance: "bg-brand-red text-white",
    advisory: "bg-brand-navy text-white"
  };

  const renderVariation1 = () => (
    <div className="max-w-4xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-brand-navy mb-6">Comprehensive AI Legal Services</h2>
      </div>

      <div className="prose prose-lg max-w-none text-brand-teal mb-8">
        <p>
          AI crosses many areas of law, including privacy, consumer protection, civil rights, and sector-specific regulations. Clarion helps its clients – including those in technology, financial services, employment, and health – adopt and govern AI in an effective, compliant, and ethical way.
        </p>
        
        <p>
          Whether you are bringing AI products to market, introducing AI into your business, or looking to manage existing workflows, we can help you:
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          {services.slice(0, 3).map((service, index) => (
            <div key={index} className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-brand-red mt-1 flex-shrink-0" />
              <p className="text-brand-teal">{service.title}</p>
            </div>
          ))}
        </div>
        <div className="space-y-4">
          {services.slice(3, 6).map((service, index) => (
            <div key={index + 3} className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-brand-red mt-1 flex-shrink-0" />
              <p className="text-brand-teal">{service.title}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderVariation2 = () => (
    <div className="max-w-6xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-brand-navy mb-6">Comprehensive AI Legal Services</h2>
        <div className="prose prose-lg max-w-4xl mx-auto text-brand-teal mb-8">
          <p>
            AI crosses many areas of law, including privacy, consumer protection, civil rights, and sector-specific regulations. Clarion helps its clients – including those in technology, financial services, employment, and health – adopt and govern AI in an effective, compliant, and ethical way.
          </p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {services.map((service, index) => {
          const IconComponent = service.icon;
          return (
            <Card key={index} className="border-2 hover:border-brand-red transition-colors duration-300">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-12 h-12 bg-brand-red rounded-full flex items-center justify-center mb-4">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-brand-teal text-center">{service.title}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderVariation3 = () => (
    <div className="max-w-6xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-brand-navy mb-6">Comprehensive AI Legal Services</h2>
        <div className="prose prose-lg max-w-4xl mx-auto text-brand-teal mb-8">
          <p>
            AI crosses many areas of law, including privacy, consumer protection, civil rights, and sector-specific regulations. Whether you are bringing AI products to market, introducing AI into your business, or looking to manage existing workflows, we can help you:
          </p>
        </div>
      </div>

      <Tabs defaultValue="governance" className="w-full">
        <TabsList className="grid w-full grid-cols-5 mb-8">
          <TabsTrigger value="governance">Governance</TabsTrigger>
          <TabsTrigger value="assessment">Assessment</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="advisory">Advisory</TabsTrigger>
        </TabsList>
        
        {['governance', 'assessment', 'contracts', 'compliance', 'advisory'].map(category => (
          <TabsContent key={category} value={category}>
            <div className="grid gap-6">
              {services
                .filter(service => service.category === category)
                .map((service, index) => {
                  const IconComponent = service.icon;
                  return (
                    <Card key={index} className="border-2 border-brand-red">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-brand-red rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <Badge className={`mb-3 ${categoryColors[category]}`}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </Badge>
                            <p className="text-brand-teal">{service.title}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  const renderVariation4 = () => (
    <div className="max-w-4xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-brand-navy mb-6">Comprehensive AI Legal Services</h2>
        <div className="prose prose-lg max-w-none text-brand-teal mb-8">
          <p>
            AI crosses many areas of law, including privacy, consumer protection, civil rights, and sector-specific regulations. Clarion helps its clients – including those in technology, financial services, employment, and health – adopt and govern AI in an effective, compliant, and ethical way.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {services.map((service, index) => {
          const IconComponent = service.icon;
          return (
            <div key={index} className="flex items-start space-x-6 p-6 bg-brand-light-gray rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red to-brand-purple rounded-full flex items-center justify-center">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="flex-grow">
                <div className="flex items-center space-x-3 mb-2">
                  <IconComponent className="w-5 h-5 text-brand-red" />
                  <Badge className={`${categoryColors[service.category]}`}>
                    {service.category.charAt(0).toUpperCase() + service.category.slice(1)}
                  </Badge>
                </div>
                <p className="text-brand-teal">{service.title}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderVariation5 = () => (
    <div className="max-w-6xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-brand-navy mb-6">How We Help You Navigate AI Legal Challenges</h2>
        <div className="prose prose-lg max-w-4xl mx-auto text-brand-teal mb-8">
          <p>
            From governance to compliance, we provide comprehensive legal support for your AI initiatives across all areas of law.
          </p>
        </div>
      </div>

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-1/2 transform -translate-x-0.5 h-full w-1 bg-brand-red opacity-30"></div>
        
        <div className="space-y-12">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            const isEven = index % 2 === 0;
            
            return (
              <div key={index} className={`flex items-center ${isEven ? 'flex-row' : 'flex-row-reverse'}`}>
                <div className={`w-1/2 ${isEven ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <Card className="border-2 hover:border-brand-red transition-colors duration-300">
                    <CardContent className="p-6">
                      <div className={`flex items-start space-x-4 ${isEven ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        <div className="w-12 h-12 bg-brand-red rounded-full flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div className={isEven ? 'text-right' : 'text-left'}>
                          <Badge className={`mb-3 ${categoryColors[service.category]}`}>
                            {service.category.charAt(0).toUpperCase() + service.category.slice(1)}
                          </Badge>
                          <p className="text-brand-teal">{service.title}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Timeline dot */}
                <div className="relative z-10 w-6 h-6 bg-brand-red rounded-full border-4 border-white shadow-lg"></div>
                
                <div className="w-1/2"></div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  const variations = [
    { name: "Original Grid", component: renderVariation1 },
    { name: "Icon Cards", component: renderVariation2 },
    { name: "Tabbed Categories", component: renderVariation3 },
    { name: "Icon List", component: renderVariation4 },
    { name: "Timeline", component: renderVariation5 }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Demo Controls */}
      <div className="bg-brand-light-gray border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <h1 className="text-brand-navy">AI Legal Services Section Variations</h1>
            <div className="flex flex-wrap gap-2">
              {variations.map((variation, index) => (
                <Button
                  key={index}
                  variant={currentVariation === index + 1 ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentVariation(index + 1)}
                  className={
                    currentVariation === index + 1
                      ? "bg-brand-red border-brand-red hover:bg-brand-red/90"
                      : "border-brand-red text-brand-red hover:bg-brand-red hover:text-white"
                  }
                >
                  {variation.name}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <section className="py-16">
        {variations[currentVariation - 1]?.component()}
      </section>
    </div>
  );
}