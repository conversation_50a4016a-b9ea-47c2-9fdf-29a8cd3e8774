import { useState } from 'react';
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import contactImage from '/public/asset/95cf43dd574944e921faf52530c2ced2154674f3.png';

export default function AcademyPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    phone: '',
    jobTitle: '',
    message: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));

    setIsSubmitted(true);
    setIsSubmitting(false);
  };

  const isEmailValid = formData.email.includes('@') && formData.email.length > 0;
  const canSubmit = isEmailValid && formData.firstName && formData.lastName;

  if (isSubmitted) {
    return (
      <div className="bg-white">
        <div className="flex">
          {/* Left side - Image */}
          <div className="hidden lg:flex lg:w-1/2">
            <div className="w-full max-h-screen relative overflow-hidden">
              <img
                src={contactImage}
                alt="Professional business consultation meeting"
                className="w-full h-full max-h-[750px] object-cover"
              />
            </div>
          </div>

          {/* Right side - Thank you message */}
          <div className="w-full lg:w-1/2 flex items-start justify-center p-8">
            <div className="max-w-md w-full text-center">
              <h1 className="text-4xl text-brand-navy mb-6">Thank you</h1>
              <p className="text-lg text-brand-navy leading-relaxed">
                Clarion will be in touch very soon.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="flex">
        {/* Left side - Image */}
        <div className="hidden lg:flex lg:w-1/2">
          <div className="w-full max-h-screen relative overflow-hidden">
            <img
              src={contactImage}
              alt="Professional business consultation meeting"
              className="w-full h-full max-h-[750px] object-cover"
            />
          </div>
        </div>

        {/* Right side - Form */}
        <div className="w-full lg:w-1/2 flex items-start justify-center p-8">
          <div className="max-w-lg w-full">
            <div className="mb-8">
              <h1 className="text-4xl text-brand-navy mb-4">Join the Academy</h1>
              <p className="text-lg text-brand-navy leading-relaxed">
                Clarion combines expert legal, technical, and strategic guidance to help you build AI you can trust. Wherever you are in your journey, we'll help you move forward and scale smart.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* First Name and Last Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-brand-navy">First Name</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-brand-navy">Last Name</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                    required
                  />
                </div>
              </div>

              {/* Email and Company */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-brand-navy">
                    Email <span className="text-brand-red">*</span>
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company" className="text-brand-navy">Company</Label>
                  <Input
                    id="company"
                    name="company"
                    type="text"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                  />
                </div>
              </div>

              {/* Phone and Job Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-brand-navy">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="jobTitle" className="text-brand-navy">Job Title</Label>
                  <Input
                    id="jobTitle"
                    name="jobTitle"
                    type="text"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    className="bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red"
                  />
                </div>
              </div>

              {/* Message */}
              <div className="space-y-2">
                <Label htmlFor="message" className="text-brand-navy">How can we help?</Label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Optional: Tell us more about what you are looking for."
                  className="min-h-[200px] bg-brand-light-gray border-transparent focus:border-brand-red focus:ring-brand-red resize-none"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!canSubmit || isSubmitting}
                  className="bg-brand-red border border-brand-red text-white hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300 px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Sending...' : 'Submit'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
