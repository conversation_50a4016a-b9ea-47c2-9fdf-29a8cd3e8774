import { <PERSON><PERSON> } from "../components/ui/button";
import { CheckCircle, GraduationCap, Search, FileText, Compass, Rocket, Target, Pause, Focus } from "lucide-react";
import { Link } from "react-router-dom";
import { TrustSignals } from '../components/TrustSignals';
import { ValueStatementsScroll } from '../components/ValueStatementsScroll';
import { ValueStatements } from '../components/ValueStatements';
import { ProcessFinal } from '../components/ProcessFinal';
import heroImage from '/public/asset/9dbc280f33e68d4935a7446439c5f47496201f1e.png';
import processImage from '/public/asset/3ddefaf7d11707df67e63e230d5d445fc0bbaef6.png';

export default function AIReadinessPage() {
    const deliverables = [
        {
            icon: GraduationCap,
            title: "Educate",
            description: "We offer AI education for leaders and teams, covering what AI is, where it creates value, and how it can introduce risk. This creates alignment and trust while preparing your organization to make informed decisions."
        },
        {
            icon: Search,
            title: "Assess",
            description: "We facilitate a holistic evaluation of your organization. We conduct interviews to better understand your aspirations, pain points, and business goals; analyze your technical infrastructure; thoroughly assess desired use cases; and consider your existing processes. These insights ensure that our recommendations are specific, realistic, and actionable."
        },
        {
            icon: FileText,
            title: "Deliver",
            description: (
                <>
                    We deliver a comprehensive <strong>AI Opportunity Roadmap</strong> summarizing findings, identifying opportunities, and providing clear recommendations.
                </>
            )
        }
    ];

    const expectations = [
        (<>The <strong>AI landscape</strong>, and what it means for your organization</>),
        (<>How AI can help you <strong>achieve your goals</strong> (and how it can't!)</>),
        (<>Whether your data and technical infrastructure are <strong>ready for AI</strong></>),
        (<>The policies and processes you need to operate AI responsibly and <strong>accelerate innovation</strong></>),
        (<>The <strong>concrete next steps</strong> your business should take</>)
    ];

    const organizationTypes = [
        (<><strong className="text-brand-navy">Exploring AI</strong> but unsure where to start</>),
        (<><strong className="text-brand-navy">Piloting tools</strong> without a clear strategy</>),
        (<><strong className="text-brand-navy">Struggling to align</strong> technical execution with business objectives</>),
        (<><strong className="text-brand-navy">Stuck midstream</strong> and need expert perspective</>),
        (<><strong className="text-brand-navy">Focused on adopting AI</strong> in a safe, scalable, and responsible way</>)
    ];

    const organizationIcons = [Compass, Rocket, Target, Pause, Focus];

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section className="relative py-24 px-4 bg-white overflow-hidden">

                {/* Background Image */}
                <div
                    className="absolute inset-0 bg-cover bg-no-repeat"
                    style={{
                        backgroundImage: `url(${heroImage})`,
                        backgroundPosition: 'center 65%',
                        opacity: 0.13
                    }}
                />

                {/* Content */}
                <div className="max-w-4xl mx-auto relative z-10">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-brand-navy leading-tight">
                            Start strong. Scale smart.
                        </h1>
                        <p className="text-xl md:text-2xl mb-10 text-brand-teal leading-relaxed">
                            Innovate with clarity and confidence with a strategy tailored to your business.
                        </p>
                        <Link to="/lets-talk">
                            <Button size="lg" className="text-lg px-8 py-4 w-48 bg-brand-red border border-brand-red hover:bg-white hover:text-brand-red hover:border-brand-red transition-colors duration-300">
                                Let's talk
                            </Button>
                        </Link>
                    </div>
                </div>
            </section>

            {/* Why It Matters Section */}
            <section className="pt-20 pb-12 px-4 bg-white">
                <div className="max-w-4xl mx-auto">
                    <h2 className="text-3xl lg:text-4xl mb-12 text-brand-navy text-center">
                        AI rewards planning. It punishes shortcuts.
                    </h2>

                    <div className="grid lg:grid-cols-5 gap-12 items-start">
                        <div className="lg:col-span-3">
                            <div className="text-base text-brand-teal leading-relaxed space-y-6">
                                <p>
                                    Organizations of all sizes are facing pressure to adopt AI. But too often, they rush to adopt off-the-shelf tools without aligning their business goals, technical infrastructure, and organizational processes. The result is risk, lackluster results, and wasted investment.
                                </p>
                                <p>
                                    Our AI Readiness program provides the foundation for success. With insight into your business, we'll use our legal, technical, and organizational expertise to ensure your AI projects are effective, show proven ROI, and scale at your pace.
                                </p>
                                <p>
                                    Whether you are starting fresh or stuck midstream, we meet you where you are and guide you forward with confidence.
                                </p>
                            </div>
                        </div>
                        <div className="lg:col-span-2">
                            <div className="bg-brand-light-gray rounded-xl p-6 lg:p-8 h-full flex flex-col justify-center">
                                <div className="text-base lg:text-lg text-brand-teal leading-relaxed">
                                    <p className="mb-4 lg:mb-6 text-lg lg:text-xl"><strong>95% of AI pilots fail, but yours doesn't have to.</strong></p>
                                    <p className="mb-4 lg:mb-6 text-sm lg:text-base">According to MIT's 2025 report <em>The GenAI Divide: State of AI in Business</em>, these failures are driven by:</p>
                                    <ul className="space-y-3 lg:space-y-4">
                                        <li className="flex items-start">
                                            <CheckCircle className="h-4 w-4 lg:h-5 lg:w-5 text-brand-red mt-1 mr-3 lg:mr-4 flex-shrink-0" />
                                            <span className="text-sm lg:text-base">Unclear objectives and poor data readiness</span>
                                        </li>
                                        <li className="flex items-start">
                                            <CheckCircle className="h-4 w-4 lg:h-5 lg:w-5 text-brand-red mt-1 mr-3 lg:mr-4 flex-shrink-0" />
                                            <span className="text-sm lg:text-base">In-house complexity and resource drain</span>
                                        </li>
                                        <li className="flex items-start">
                                            <CheckCircle className="h-4 w-4 lg:h-5 lg:w-5 text-brand-red mt-1 mr-3 lg:mr-4 flex-shrink-0" />
                                            <span className="text-sm lg:text-base">Executive pressure leading to premature launches</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Trust Signals */}
            <TrustSignals />

            {/* Value Statements Scroll */}

            <ValueStatements />

            {/* What You Get Section */}
            <section className="py-20 px-4 bg-slate-50">
                <div className="max-w-4xl mx-auto">
                    <h2 className="text-3xl lg:text-4xl mb-12 text-brand-navy text-center">
                        What you can expect
                    </h2>
                    <div className="text-lg text-brand-teal leading-relaxed">
                        <p className="mb-6 text-center">Our experts will help you build a clear understanding of</p>
                        <ul className="space-y-4 max-w-3xl mx-auto">
                            {expectations.map((expectation, index) => (
                                <li key={index} className="flex items-start">
                                    <CheckCircle className="h-6 w-6 text-brand-red mt-1 mr-4 flex-shrink-0" />
                                    <span className="[&_strong]:text-brand-navy">{expectation}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </section>

            {/* Core Deliverables Section */}
            <ProcessFinal />

            {/* Who We Help Section */}
            <section className="relative py-20 px-4 bg-slate-50 overflow-hidden">
                {/* Background Image */}
                <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5"
                    style={{
                        backgroundImage: `url(${processImage})`
                    }}
                />

                {/* Content */}
                <div className="max-w-4xl mx-auto relative z-10">
                    <h2 className="text-3xl lg:text-4xl mb-8 text-brand-navy text-center">
                        We meet you where you are
                    </h2>
                    <p className="text-lg text-brand-teal mb-8 text-center leading-relaxed">
                        The AI Readiness program is designed for organizations that are:
                    </p>

                    <div className="flex justify-center">
                        <div className="space-y-4 max-w-2xl">
                            {organizationTypes.map((type, index) => {
                                const IconComponent = organizationIcons[index];
                                return (
                                    <div key={index} className="flex items-center">
                                        <IconComponent className="h-6 w-6 text-brand-red mr-4 flex-shrink-0" />
                                        <span className="text-base text-brand-teal">{type}</span>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 px-4 bg-brand-orange/90 text-white">
                <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-3xl lg:text-4xl mb-6 text-white">
                        Move forward with AI
                    </h2>
                    <p className="text-xl text-white mb-8 leading-relaxed">
                        Clarion gives you the clarity and strategic footing to adopt AI responsibly. Whether you are testing, planning, or stuck in the middle, we will help you take your next step with confidence.
                    </p>
                    <Link to="/lets-talk">
                        <Button size="lg" className="text-lg px-8 py-4 w-48 bg-white text-brand-orange hover:bg-slate-50 border border-white transition-colors duration-300">
                            Let's talk
                        </Button>
                    </Link>
                </div>
            </section>
        </div>
    );
}
