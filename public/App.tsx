import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import React, { useEffect } from 'react';
import { Header } from "./components/Header";
import { Footer } from "./components/Footer";
import HomePage from "./pages/HomePage";
import AIReadinessPage from "./pages/AIReadinessPage";
import AIBuildsPage from "./pages/AIBuildsPage";
import AICounselPage from "./pages/AICounselPage";
import AboutPage from "./pages/AboutPage";
import LetsTalkPage from "./pages/LetsTalkPage";
import AcademyPage from "./pages/AcademyPage";
import ExpectationsDemo from "./pages/ExpectationsDemo";
import ReactDOM from "react-dom/client";

// Component to handle scroll to top on route changes
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

export default function App() {
  return (
    <Router>
      <div className="min-h-screen">
        <ScrollToTop />
        <Header />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/ai-readiness" element={<AIReadinessPage />} />
          <Route path="/ai-builds" element={<AIBuildsPage />} />
          <Route path="/ai-counsel" element={<AICounselPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/lets-talk" element={<LetsTalkPage />} />
          <Route path="/academy" element={<AcademyPage />} />
          {/*<Route path="/expectations-demo" element={<ExpectationsDemo />} />*/}

          <Route path="/preview_page.html" element={<Navigate to="/" replace />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        <Footer />
      </div>
    </Router>
  );
}
ReactDOM.createRoot(document.getElementById('app') as HTMLElement).render(
    <React.StrictMode>
        <App />
    </React.StrictMode>
);
